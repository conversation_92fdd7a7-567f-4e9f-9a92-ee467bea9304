const request = require('supertest');
const app = require('../index');
const { User, Role, Eleveur, Ouvrier } = require('../models');
const AuthService = require('../services/authService');
const bcrypt = require('bcryptjs');

describe('Ouvrier API Tests', () => {
  let adminToken, eleveurToken, ouvrierToken;
  let adminUser, eleveurUser, ouvrierUser;
  let eleveur, ouvrier;

  beforeAll(async () => {
    // Créer les rôles nécessaires
    const adminRole = await Role.create({
      name: 'admin',
      description: 'Admin role',
      permissions: ['manage_users', 'manage_ouvriers']
    });

    const eleveurRole = await Role.create({
      name: 'eleveur',
      description: 'Eleveur role',
      permissions: ['manage_own_ouvriers']
    });

    const ouvrierRole = await Role.create({
      name: 'ouvrier',
      description: 'Ouvrier role',
      permissions: ['view_own_profile']
    });

    // Créer un admin
    adminUser = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password: await bcrypt.hash('Admin123!', 10),
      role_id: adminRole.id,
      status: 'active'
    });
    adminToken = await adminUser.generateToken();

    // Créer un éleveur
    eleveur = await Eleveur.create({
      nom: 'Test',
      prenom: 'Eleveur',
      email: '<EMAIL>',
      telephone: '**********',
      adresse: 'Test address'
    });

    eleveurUser = await User.create({
      username: 'testeleveur',
      email: '<EMAIL>',
      password: await bcrypt.hash('Eleveur123!', 10),
      role_id: eleveurRole.id,
      profile_id: eleveur.id,
      status: 'active'
    });
    eleveurToken = await eleveurUser.generateToken();
  });

  afterAll(async () => {
    // Nettoyer la base de données
    await User.destroy({ where: {} });
    await Role.destroy({ where: {} });
    await Eleveur.destroy({ where: {} });
    await Ouvrier.destroy({ where: {} });
  });

  describe('POST /api/ouvriers', () => {
    it('should create a new ouvrier with user account when admin', async () => {
      const response = await request(app)
        .post('/api/ouvriers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          nom: 'Test',
          prenom: 'Ouvrier',
          email: '<EMAIL>',
          password: 'Ouvrier123!',
          telephone: '**********',
          adresse: 'Test address',
          date_embauche: new Date(),
          salaire: 30000,
          eleveur_id: eleveur.id
        });

      expect(response.status).toBe(201);
      expect(response.body.ouvrier).toBeDefined();
      expect(response.body.user).toBeDefined();
      expect(response.body.user.role).toBe('ouvrier');
    });

    it('should create a new ouvrier when eleveur', async () => {
      const response = await request(app)
        .post('/api/ouvriers')
        .set('Authorization', `Bearer ${eleveurToken}`)
        .send({
          nom: 'Test2',
          prenom: 'Ouvrier2',
          email: '<EMAIL>',
          password: 'Ouvrier123!',
          telephone: '0123456780',
          adresse: 'Test address 2',
          date_embauche: new Date(),
          salaire: 35000
        });

      expect(response.status).toBe(201);
      expect(response.body.ouvrier.eleveur_id).toBe(eleveur.id);
    });

    it('should not create ouvrier with duplicate email', async () => {
      const response = await request(app)
        .post('/api/ouvriers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          nom: 'Test3',
          prenom: 'Ouvrier3',
          email: '<EMAIL>', // Email déjà utilisé
          password: 'Ouvrier123!',
          telephone: '0123456781',
          adresse: 'Test address 3',
          date_embauche: new Date(),
          salaire: 32000,
          eleveur_id: eleveur.id
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('déjà utilisé');
    });
  });

  describe('GET /api/ouvriers', () => {
    it('should get all ouvriers when admin', async () => {
      const response = await request(app)
        .get('/api/ouvriers')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should get only own ouvriers when eleveur', async () => {
      const response = await request(app)
        .get('/api/ouvriers')
        .set('Authorization', `Bearer ${eleveurToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.every(o => o.eleveur_id === eleveur.id)).toBe(true);
    });
  });

  describe('GET /api/ouvriers/:id', () => {
    let testOuvrier;

    beforeAll(async () => {
      const { ouvrier } = await AuthService.createOuvrierWithAccount({
        nom: 'Test',
        prenom: 'GetOuvrier',
        email: '<EMAIL>',
        password: 'Ouvrier123!',
        telephone: '**********',
        adresse: 'Test address get',
        date_embauche: new Date(),
        salaire: 33000
      }, eleveur.id);
      testOuvrier = ouvrier;
    });

    it('should get ouvrier by id when admin', async () => {
      const response = await request(app)
        .get(`/api/ouvriers/${testOuvrier.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testOuvrier.id);
    });

    it('should get own ouvrier when eleveur', async () => {
      const response = await request(app)
        .get(`/api/ouvriers/${testOuvrier.id}`)
        .set('Authorization', `Bearer ${eleveurToken}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testOuvrier.id);
      expect(response.body.eleveur_id).toBe(eleveur.id);
    });

    it('should not get ouvrier from different eleveur', async () => {
      // Créer un autre éleveur
      const otherEleveur = await Eleveur.create({
        nom: 'Other',
        prenom: 'Eleveur',
        email: '<EMAIL>',
        telephone: '0123456783',
        adresse: 'Other address'
      });

      const otherEleveurUser = await User.create({
        username: 'othereleveur',
        email: '<EMAIL>',
        password: await bcrypt.hash('Eleveur123!', 10),
        role_id: eleveurUser.role_id,
        profile_id: otherEleveur.id,
        status: 'active'
      });
      const otherEleveurToken = await otherEleveurUser.generateToken();

      const response = await request(app)
        .get(`/api/ouvriers/${testOuvrier.id}`)
        .set('Authorization', `Bearer ${otherEleveurToken}`);

      expect(response.status).toBe(403);
    });
  });
});