import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/login_screen.dart';
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/splash/splash_screen.dart';

class AppNavigator extends ConsumerWidget {
  const AppNavigator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    return Navigator(
      pages: [
        // Splash Screen - Always shown first while checking auth state
        if (!authState.isAuthenticated && authState.user == null)
          const MaterialPage(
            key: ValueKey('splash'),
            child: SplashScreen(),
          ),

        // Auth Flow
        if (!authState.isAuthenticated && authState.user == null)
          const MaterialPage(
            key: ValueKey('login'),
            child: LoginScreen(),
          ),

        // Main App Flow
        if (authState.isAuthenticated && authState.user != null)
          MaterialPage(
            key: const ValueKey('dashboard'),
            child: DashboardScreen(user: authState.user!),
          ),
      ],
      onPopPage: (route, result) {
        if (!route.didPop(result)) {
          return false;
        }

        return true;
      },
    );
  }
}

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  
  // Éleveur Routes
  static const String farms = '/farms';
  static const String farmDetails = '/farms/:id';
  static const String batiments = '/batiments';
  static const String batimentDetails = '/batiments/:id';
  static const String lots = '/lots';
  static const String lotDetails = '/lots/:id';
  
  // Vétérinaire Routes
  static const String consultations = '/consultations';
  static const String consultationDetails = '/consultations/:id';
  static const String prescriptions = '/prescriptions';
  static const String prescriptionDetails = '/prescriptions/:id';
  
  // Marchand Routes
  static const String products = '/products';
  static const String productDetails = '/products/:id';
  static const String orders = '/orders';
  static const String orderDetails = '/orders/:id';
  
  // Finance Routes
  static const String transactions = '/transactions';
  static const String transactionDetails = '/transactions/:id';
  static const String expenses = '/expenses';
  static const String expenseDetails = '/expenses/:id';
  static const String revenues = '/revenues';
  static const String revenueDetails = '/revenues/:id';
  static const String budget = '/budget';
  static const String reports = '/reports';
  
  // Admin Routes
  static const String users = '/users';
  static const String userDetails = '/users/:id';
  static const String roles = '/roles';
  static const String roleDetails = '/roles/:id';
  static const String analytics = '/analytics';
  
  static String replaceId(String route, String id) {
    return route.replaceAll(':id', id);
  }
}