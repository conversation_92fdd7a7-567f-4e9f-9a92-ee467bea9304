import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_ios/local_auth_ios.dart';
import '../utils/app_config.dart';

class BiometricService {
  final LocalAuthentication _auth;

  BiometricService() : _auth = LocalAuthentication();

  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _auth.canCheckBiometrics;
      final isDeviceSupported = await _auth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _auth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  Future<bool> authenticate() async {
    try {
      return await _auth.authenticate(
        localizedReason: 'Veuillez vous authentifier pour continuer',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
          useErrorDialogs: true,
        ),
        authMessages: const [
          AndroidAuthMessages(
            signInTitle: 'Authentification biométrique',
            cancelButton: 'Annuler',
            biometricHint: 'Vérifiez votre identité',
            biometricNotRecognized: 'Biométrie non reconnue',
            biometricSuccess: 'Biométrie reconnue',
            biometricRequiredTitle: 'Biométrie requise',
          ),
          IOSAuthMessages(
            cancelButton: 'Annuler',
            goToSettingsButton: 'Paramètres',
            goToSettingsDescription: 'Veuillez configurer la biométrie',
            lockOut: 'Veuillez réactiver la biométrie',
          ),
        ],
      );
    } catch (e) {
      return false;
    }
  }

  Future<bool> authenticateWithTimeout() async {
    try {
      return await Future.any([
        authenticate(),
        Future.delayed(AppConfig.biometricTimeout, () => false),
      ]);
    } catch (e) {
      return false;
    }
  }

  Future<void> stopAuthentication() async {
    await _auth.stopAuthentication();
  }
}

final biometricServiceProvider = Provider<BiometricService>((ref) {
  return BiometricService();
});