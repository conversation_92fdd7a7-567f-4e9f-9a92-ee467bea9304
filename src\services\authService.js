const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User, Role, Eleveur, Ouvrier } = require('../models');

class AuthService {
  static async createUser(userData, role = 'user') {
    try {
      // Vérifier si l'email existe déjà
      const existingUser = await User.findOne({ where: { email: userData.email } });
      if (existingUser) {
        throw new Error('Cet email est déjà utilisé');
      }

      // Vérifier si le nom d'utilisateur existe déjà
      const existingUsername = await User.findOne({ where: { username: userData.username } });
      if (existingUsername) {
        throw new Error('Ce nom d\'utilisateur est déjà utilisé');
      }

      // Récupérer le rôle
      const userRole = await Role.findOne({ where: { name: role } });
      if (!userRole) {
        throw new Error('Rôle non trouvé');
      }

      // Créer l'utilisateur
      const user = await User.create({
        ...userData,
        role_id: userRole.id,
        status: 'active'
      });

      return user;
    } catch (error) {
      console.error('Erreur dans createUser:', error);
      throw error;
    }
  }

  static async createOuvrierWithAccount(ouvrierData, eleveurId) {
    try {
      // Vérifier si l'éleveur existe
      const eleveur = await Eleveur.findByPk(eleveurId);
      if (!eleveur) {
        throw new Error('Éleveur non trouvé');
      }

      // Créer un compte utilisateur pour l'ouvrier
      const userData = {
        username: ouvrierData.email.split('@')[0], // Utiliser la première partie de l'email comme username
        email: ouvrierData.email,
        password: await bcrypt.hash(ouvrierData.password, 10),
        first_name: ouvrierData.nom,
        last_name: ouvrierData.prenom,
        phone: ouvrierData.telephone
      };

      const user = await this.createUser(userData, 'ouvrier');

      // Créer le profil ouvrier
      const ouvrier = await Ouvrier.create({
        ...ouvrierData,
        eleveur_id: eleveurId,
        user_id: user.id
      });

      // Mettre à jour le profile_id de l'utilisateur
      await user.update({ profile_id: ouvrier.id });

      return {
        user,
        ouvrier
      };
    } catch (error) {
      console.error('Erreur dans createOuvrierWithAccount:', error);
      throw error;
    }
  }

  static async login(email, password) {
    try {
      // Trouver l'utilisateur avec son rôle
      const user = await User.findOne({
        where: { email },
        include: [{
          model: Role,
          as: 'role',
          attributes: ['name']
        }]
      });

      if (!user) {
        throw new Error('Identifiants invalides');
      }

      // Vérifier le mot de passe
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        throw new Error('Identifiants invalides');
      }

      // Vérifier si le compte est actif
      if (user.status !== 'active') {
        throw new Error('Ce compte est ' + user.status);
      }

      // Générer le token JWT
      const token = await user.generateToken();

      return {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role.name,
          first_name: user.first_name,
          last_name: user.last_name,
          status: user.status
        },
        token
      };
    } catch (error) {
      console.error('Erreur dans login:', error);
      throw error;
    }
  }

  static async changePassword(userId, oldPassword, newPassword) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('Utilisateur non trouvé');
      }

      // Vérifier l'ancien mot de passe
      const isValidPassword = await bcrypt.compare(oldPassword, user.password);
      if (!isValidPassword) {
        throw new Error('Ancien mot de passe incorrect');
      }

      // Mettre à jour le mot de passe
      await user.update({
        password: await bcrypt.hash(newPassword, 10)
      });

      return true;
    } catch (error) {
      console.error('Erreur dans changePassword:', error);
      throw error;
    }
  }

  static async resetPassword(email) {
    try {
      const user = await User.findOne({ where: { email } });
      if (!user) {
        throw new Error('Utilisateur non trouvé');
      }

      // Générer un token de réinitialisation temporaire
      const resetToken = jwt.sign(
        { id: user.id },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      // TODO: Envoyer l'email avec le lien de réinitialisation
      // Cette partie dépendra de votre service d'envoi d'emails

      return true;
    } catch (error) {
      console.error('Erreur dans resetPassword:', error);
      throw error;
    }
  }
}

module.exports = AuthService;