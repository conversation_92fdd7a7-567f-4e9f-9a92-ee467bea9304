/**
 * Composant de gestion des dispositifs IoT pour les éleveurs
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Switch,
  FormControlLabel,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PowerSettingsNew as PowerIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  DeviceHub as DeviceIcon,
  Sensors as SensorsIcon,
  Thermostat as TempIcon,
  Water as HumidityIcon,
  Air as AirIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { iotAPI } from '../../services/api';

const IoTDevicesManagement = () => {
  const { user } = useAuth();
  const [devices, setDevices] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [formData, setFormData] = useState({
    nom: '',
    type: '',
    localisation: '',
    seuil_min: '',
    seuil_max: '',
    actif: true
  });

  const [stats, setStats] = useState({
    totalDevices: 0,
    activeDevices: 0,
    alertsCount: 0,
    offlineDevices: 0
  });

  useEffect(() => {
    loadDevices();
    loadAlerts();
    loadStats();

    // Actualiser les données toutes les 30 secondes
    const interval = setInterval(() => {
      loadDevices();
      loadAlerts();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadDevices = async () => {
    try {
      setLoading(true);
      const response = await iotAPI.getDevices();
      setDevices(Array.isArray(response.data) ? response.data : []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des dispositifs');
      console.error('Erreur dispositifs IoT:', err);
      setDevices([]); // Reset to empty array on error
    } finally {
      setLoading(false);
    }
  };

  const loadAlerts = async () => {
    try {
      const response = await iotAPI.getAlerts();
      setAlerts(response.data || []);
    } catch (err) {
      console.error('Erreur alertes IoT:', err);
    }
  };

  const loadStats = async () => {
    try {
      const response = await iotAPI.getStats();
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats IoT:', err);
    }
  };

  const handleOpenDialog = (device = null) => {
    if (device) {
      setSelectedDevice(device);
      setFormData({
        nom: device.nom || '',
        type: device.type || '',
        localisation: device.localisation || '',
        seuil_min: device.seuil_min || '',
        seuil_max: device.seuil_max || '',
        actif: device.actif !== false
      });
    } else {
      setSelectedDevice(null);
      setFormData({
        nom: '',
        type: '',
        localisation: '',
        seuil_min: '',
        seuil_max: '',
        actif: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedDevice(null);
  };

  const handleSubmit = async () => {
    try {
      const deviceData = {
        ...formData,
        eleveur_id: user.profile_id
      };

      if (selectedDevice) {
        await iotAPI.updateDevice(selectedDevice.id, deviceData);
      } else {
        await iotAPI.createDevice(deviceData);
      }

      handleCloseDialog();
      loadDevices();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde du dispositif');
      console.error('Erreur sauvegarde dispositif:', err);
    }
  };

  const handleToggleDevice = async (deviceId, currentStatus) => {
    try {
      await iotAPI.toggleDevice(deviceId, !currentStatus);
      loadDevices();
      loadStats();
    } catch (err) {
      setError('Erreur lors du changement de statut');
      console.error('Erreur toggle device:', err);
    }
  };

  const handleDelete = async (deviceId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce dispositif ?')) {
      try {
        await iotAPI.deleteDevice(deviceId);
        loadDevices();
        loadStats();
      } catch (err) {
        setError('Erreur lors de la suppression du dispositif');
        console.error('Erreur suppression dispositif:', err);
      }
    }
  };

  const getDeviceIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'temperature': return <TempIcon />;
      case 'humidity': return <HumidityIcon />;
      case 'air_quality': return <AirIcon />;
      case 'sensor': return <SensorsIcon />;
      default: return <DeviceIcon />;
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'online': return 'success';
      case 'offline': return 'error';
      case 'warning': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status?.toLowerCase()) {
      case 'online': return 'En ligne';
      case 'offline': return 'Hors ligne';
      case 'warning': return 'Alerte';
      default: return status;
    }
  };

  const StatCard = ({ title, value, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: `${color}.light`,
            color: `${color}.contrastText`,
            mr: 2
          }}>
            {icon}
          </Box>
          <Box>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h5">
              {value}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des dispositifs IoT...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Gestion des Dispositifs IoT
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              loadDevices();
              loadAlerts();
              loadStats();
            }}
          >
            Actualiser
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Ajouter Dispositif
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Alertes actives */}
      {alerts.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Alertes Actives ({alerts.length})
          </Typography>
          {alerts.slice(0, 3).map((alert, index) => (
            <Box key={index} sx={{ mt: 1 }}>
              <Typography variant="body2">
                <strong>{alert.device_nom}:</strong> {alert.message}
              </Typography>
            </Box>
          ))}
          {alerts.length > 3 && (
            <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
              ... et {alerts.length - 3} autres alertes
            </Typography>
          )}
        </Alert>
      )}

      {/* Statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Dispositifs"
            value={stats.totalDevices}
            icon={<DeviceIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Actifs"
            value={stats.activeDevices}
            icon={<CheckIcon />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Alertes"
            value={stats.alertsCount}
            icon={<WarningIcon />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Hors Ligne"
            value={stats.offlineDevices}
            icon={<ErrorIcon />}
            color="error"
          />
        </Grid>
      </Grid>

      {/* Table des dispositifs */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Liste des Dispositifs
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Dispositif</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Localisation</TableCell>
                  <TableCell>Statut</TableCell>
                  <TableCell>Dernière Valeur</TableCell>
                  <TableCell>Seuils</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {(devices || []).map((device) => (
                  <TableRow key={device.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getDeviceIcon(device.type)}
                        <Box sx={{ ml: 2 }}>
                          <Typography variant="body2" fontWeight="bold">
                            {device.nom}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {device.device_id}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{device.type}</TableCell>
                    <TableCell>{device.localisation}</TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(device.statut)}
                        color={getStatusColor(device.statut)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {device.derniere_valeur ? (
                        <Box>
                          <Typography variant="body2">
                            {device.derniere_valeur} {device.unite}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {device.derniere_lecture && new Date(device.derniere_lecture).toLocaleString('fr-FR')}
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Aucune donnée
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {device.seuil_min && device.seuil_max ? (
                        <Typography variant="body2">
                          {device.seuil_min} - {device.seuil_max}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Non configuré
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Tooltip title={device.actif ? 'Désactiver' : 'Activer'}>
                        <IconButton
                          size="small"
                          onClick={() => handleToggleDevice(device.id, device.actif)}
                          color={device.actif ? 'success' : 'default'}
                        >
                          <PowerIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Modifier">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(device)}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Supprimer">
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(device.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Dialog pour ajouter/modifier un dispositif */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedDevice ? 'Modifier le Dispositif' : 'Nouveau Dispositif'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nom du Dispositif"
                value={formData.nom}
                onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                >
                  <MenuItem value="temperature">Température</MenuItem>
                  <MenuItem value="humidity">Humidité</MenuItem>
                  <MenuItem value="air_quality">Qualité de l'air</MenuItem>
                  <MenuItem value="sensor">Capteur générique</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Localisation"
                value={formData.localisation}
                onChange={(e) => setFormData({ ...formData, localisation: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Seuil Minimum"
                type="number"
                value={formData.seuil_min}
                onChange={(e) => setFormData({ ...formData, seuil_min: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Seuil Maximum"
                type="number"
                value={formData.seuil_max}
                onChange={(e) => setFormData({ ...formData, seuil_max: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.actif}
                    onChange={(e) => setFormData({ ...formData, actif: e.target.checked })}
                  />
                }
                label="Dispositif actif"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {selectedDevice ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default IoTDevicesManagement;
