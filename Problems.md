[{
"resource": "/K:/Projets*Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'package:flutter_gen/gen_l10n/app_localizations.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 5,
"startColumn": 8,
"endLineNumber": 5,
"endColumn": 61,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'utils/app_theme.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 8,
"startColumn": 8,
"endLineNumber": 8,
"endColumn": 30,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'providers/finance_provider.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 10,
"startColumn": 8,
"endLineNumber": 10,
"endColumn": 41,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/auth/login_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 11,
"startColumn": 8,
"endLineNumber": 11,
"endColumn": 40,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/dashboard/dashboard_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 12,
"startColumn": 8,
"endLineNumber": 12,
"endColumn": 49,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/splash/splash_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 13,
"startColumn": 8,
"endLineNumber": 13,
"endColumn": 43,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/finance/finance_dashboard_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 14,
"startColumn": 8,
"endLineNumber": 14,
"endColumn": 55,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/finance/transactions_list_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 15,
"startColumn": 8,
"endLineNumber": 15,
"endColumn": 55,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/finance/add_edit_transaction_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 16,
"startColumn": 8,
"endLineNumber": 16,
"endColumn": 58,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/finance/budget_planning_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 17,
"startColumn": 8,
"endLineNumber": 17,
"endColumn": 53,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "uri*does_not_exist",
"target": {
"$mid": 1,
"path": "/diagnostics/uri_does_not_exist",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Target of URI doesn't exist: 'screens/finance/financial_reports_screen.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
"source": "dart",
"startLineNumber": 18,
"startColumn": 8,
"endLineNumber": 18,
"endColumn": 55,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "undefined*identifier",
"target": {
"$mid": 1,
"path": "/diagnostics/undefined_identifier",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Undefined name 'AppTheme'.\nTry correcting the name to one that is defined, or defining the name.",
"source": "dart",
"startLineNumber": 24,
"startColumn": 3,
"endLineNumber": 24,
"endColumn": 11,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "undefined*identifier",
"target": {
"$mid": 1,
"path": "/diagnostics/undefined_identifier",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Undefined name 'AppTheme'.\nTry correcting the name to one that is defined, or defining the name.",
"source": "dart",
"startLineNumber": 46,
"startColumn": 14,
"endLineNumber": 46,
"endColumn": 22,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "undefined*identifier",
"target": {
"$mid": 1,
"path": "/diagnostics/undefined_identifier",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "Undefined name 'AppLocalizations'.\nTry correcting the name to one that is defined, or defining the name.",
"source": "dart",
"startLineNumber": 50,
"startColumn": 9,
"endLineNumber": 50,
"endColumn": 25,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "non*constant_list_element",
"target": {
"$mid": 1,
"path": "/diagnostics/non_constant_list_element",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The values in a const list literal must be constants.\nTry removing the keyword 'const' from the list literal.",
"source": "dart",
"startLineNumber": 50,
"startColumn": 9,
"endLineNumber": 50,
"endColumn": 25,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'LoginScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 68,
"startColumn": 38,
"endLineNumber": 68,
"endColumn": 49,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'DashboardScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 69,
"startColumn": 42,
"endLineNumber": 69,
"endColumn": 57,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'FinanceDashboardScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 70,
"startColumn": 40,
"endLineNumber": 70,
"endColumn": 62,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'TransactionsListScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 71,
"startColumn": 53,
"endLineNumber": 71,
"endColumn": 75,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'AddEditTransactionScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 72,
"startColumn": 56,
"endLineNumber": 72,
"endColumn": 80,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'BudgetPlanningScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 73,
"startColumn": 47,
"endLineNumber": 73,
"endColumn": 67,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'FinancialReportsScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 74,
"startColumn": 48,
"endLineNumber": 74,
"endColumn": 70,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'LoginScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 80,
"startColumn": 39,
"endLineNumber": 80,
"endColumn": 50,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "undefined*method",
"target": {
"$mid": 1,
"path": "/diagnostics/undefined_method",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The method 'initialize' isn't defined for the type 'AuthNotifier'.\nTry correcting the name to the name of an existing method, or defining a method named 'initialize'.",
"source": "dart",
"startLineNumber": 112,
"startColumn": 39,
"endLineNumber": 112,
"endColumn": 49,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'SplashScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 122,
"startColumn": 20,
"endLineNumber": 122,
"endColumn": 32,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'DashboardScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 127,
"startColumn": 20,
"endLineNumber": 127,
"endColumn": 35,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "creation*with_non_type",
"target": {
"$mid": 1,
"path": "/diagnostics/creation_with_non_type",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 8,
"message": "The name 'LoginScreen' isn't a class.\nTry correcting the name to match an existing class.",
"source": "dart",
"startLineNumber": 129,
"startColumn": 20,
"endLineNumber": 129,
"endColumn": 31,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": "todo",
"severity": 2,
"message": "TODO: Get farmId from context",
"source": "dart",
"startLineNumber": 70,
"startColumn": 78,
"endLineNumber": 70,
"endColumn": 107,
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets*Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "deprecated*member_use",
"target": {
"$mid": 1,
"path": "/diagnostics/deprecated_member_use",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 2,
"message": "'textScaleFactor' is deprecated and shouldn't be used. Use textScaler instead. Use of textScaleFactor was deprecated in preparation for the upcoming nonlinear text scaling support. This feature was deprecated after v3.12.0-2.0.pre.\nTry replacing the use of the deprecated member with the replacement.",
"source": "dart",
"startLineNumber": 89,
"startColumn": 13,
"endLineNumber": 89,
"endColumn": 28,
"tags": [
2
],
"extensionID": "Dart-Code.dart-code"
},{
"resource": "/K:/Projets_Sites_Web/Poultraydz-07July2025/poultry_dz_mobile/lib/main.dart",
"owner": "\_generated_diagnostic_collection_name*#12",
"code": {
"value": "deprecated_member_use",
"target": {
"$mid": 1,
"path": "/diagnostics/deprecated_member_use",
"scheme": "https",
"authority": "dart.dev"
}
},
"severity": 2,
"message": "'textScaleFactor' is deprecated and shouldn't be used. Use textScaler instead. Use of textScaleFactor was deprecated in preparation for the upcoming nonlinear text scaling support. This feature was deprecated after v3.12.0-2.0.pre.\nTry replacing the use of the deprecated member with the replacement.",
"source": "dart",
"startLineNumber": 89,
"startColumn": 53,
"endLineNumber": 89,
"endColumn": 68,
"tags": [
2
],
"extensionID": "Dart-Code.dart-code"
}]
