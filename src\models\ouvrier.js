const { DataTypes, Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Ouvrier extends Model {
    static associate(models) {
      // Association avec Eleveur
      Ouvrier.belongsTo(models.Eleveur, {
        foreignKey: 'eleveur_id',
        as: 'eleveur'
      });

      // Association avec User
      Ouvrier.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user'
      });
    }
  }

  Ouvrier.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  eleveur_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Eleveurs',
      key: 'id'
    }
  },
  nom: {
    type: DataTypes.STRING,
    allowNull: false
  },
  prenom: {
    type: DataTypes.STRING,
    allowNull: false
  },
  telephone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  adresse: {
    type: DataTypes.STRING,
    allowNull: true
  },
  date_embauche: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  salaire: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  statut: {
    type: DataTypes.ENUM('actif', 'inactif'),
    defaultValue: 'actif'
  },
  role: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'ouvrier'
  },
  horaires_travail: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Horaires de travail en format JSON'
  },
  competences: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: []
  }
  }, {
    sequelize,
    modelName: 'Ouvrier',
    tableName: 'Ouvriers',
    timestamps: true,
    indexes: [
      {
        fields: ['eleveur_id']
      },
      {
        fields: ['telephone']
      }
    ]
  });

  // Méthodes statiques
  Ouvrier.findByEleveur = async function(eleveurId) {
    return await this.findAll({
      where: { eleveur_id: eleveurId },
      include: [{
        model: sequelize.models.Eleveur,
        as: 'eleveur',
        attributes: ['nom', 'prenom', 'email']
      }]
    });
  };

  return Ouvrier;
};
