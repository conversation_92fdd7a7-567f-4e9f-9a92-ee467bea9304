import 'user_model.dart';

class AuthResponse {
  final User user;
  final String accessToken;
  final String refreshToken;

  AuthResponse({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      user: User.from<PERSON><PERSON>(json['user']),
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
    );
  }
}

class AuthResult {
  final bool success;
  final String? message;
  final User? user;
  final Map<String, List<String>>? errors;

  AuthResult.success({this.user})
      : success = true,
        message = null,
        errors = null;

  AuthResult.failure({this.message, this.errors})
      : success = false,
        user = null;
}

class RegisterRequest {
  final String name;
  final String email;
  final String password;
  final String? phone;

  RegisterRequest({
    required this.name,
    required this.email,
    required this.password,
    this.phone,
  });

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'name': name,
      'email': email,
      'password': password,
      if (phone != null) 'phone': phone,
    };
  }
}