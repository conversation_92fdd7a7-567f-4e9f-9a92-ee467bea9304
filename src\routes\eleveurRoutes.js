// Enhanced Éleveur Dashboard Routes
const express = require('express');
const router = express.Router();
const { body, param, query, validationResult } = require('express-validator');
const { auth, checkRole } = require('../middleware/auth');
const validatePagination = require('../middleware/pagination');
const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const bcrypt = require('bcryptjs');

// Helper function to get or create eleveur ID for a user
async function getOrCreateEleveurId(user) {
  try {
    // Chercher un éleveur existant avec l'email de l'utilisateur
    const existingEleveur = await sequelize.query(
      'SELECT id FROM eleveurs WHERE email = $1',
      {
        bind: [user.email],
        type: QueryTypes.SELECT
      }
    );

    if (existingEleveur.length > 0) {
      const eleveurId = existingEleveur[0].id;

      // Mettre à jour le profile_id de l'utilisateur
      await sequelize.query(
        'UPDATE users SET profile_id = $1 WHERE id = $2',
        {
          bind: [eleveurId, user.id],
          type: QueryTypes.UPDATE
        }
      );

      console.log(`✅ Profile ID mis à jour pour l'utilisateur ${user.id}: ${eleveurId}`);
      return eleveurId;
    } else {
      // Créer un nouvel éleveur si aucun n'existe
      const newEleveur = await sequelize.query(
        `INSERT INTO eleveurs (nom, prenom, email, telephone, adresse, date_inscription, date_modification, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, NOW(), NOW(), NOW(), NOW()) RETURNING id`,
        {
          bind: [
            user.last_name || 'Nom',
            user.first_name || 'Prénom',
            user.email,
            '0000000000',
            'Adresse à compléter'
          ],
          type: QueryTypes.INSERT
        }
      );

      const eleveurId = newEleveur[0][0].id;

      // Mettre à jour le profile_id de l'utilisateur
      await sequelize.query(
        'UPDATE users SET profile_id = $1 WHERE id = $2',
        {
          bind: [eleveurId, user.id],
          type: QueryTypes.UPDATE
        }
      );

      console.log(`✅ Nouvel éleveur créé et profile ID mis à jour: ${eleveurId}`);
      return eleveurId;
    }
  } catch (error) {
    console.error('Erreur lors de la création/récupération de l\'éleveur:', error);
    return null;
  }
}

// Logger pour les requêtes SQL
const logQuery = (query, params) => {
  console.log('SQL Query:', {
    text: query,
    params,
    timestamp: new Date().toISOString()
  });
};

// Middleware pour vérifier si l'utilisateur est un éleveur
const isEleveur = async (req, res, next) => {
  try {
    if (req.user.role !== 'eleveur' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès refusé. Rôle éleveur requis.'
      });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isEleveur:', error);
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur serveur'
    });
  }
};

// Middleware pour vérifier si l'utilisateur est un ouvrier/fonctionnaire
const isOuvrier = async (req, res, next) => {
  try {
    if (req.user.role !== 'ouvrier' && req.user.role !== 'eleveur' && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        code: 'FORBIDDEN',
        message: 'Accès refusé. Rôle ouvrier ou éleveur requis.'
      });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isOuvrier:', error);
    res.status(500).json({
      status: 'error',
      code: 'SERVER_ERROR',
      message: 'Erreur serveur'
    });
  }
};

// @route   GET /api/eleveurs
// @desc    Get all éleveurs (admin only) or current éleveur info
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Si l'utilisateur est admin, retourner tous les éleveurs
    if (req.user.role === 'admin') {
      const eleveurs = await sequelize.query(
        `SELECT
          e.id,
          e.nom,
          e.prenom,
          e.telephone,
          e.email,
          e.adresse,
          e.date_creation,
          COUNT(v.id) as nombre_volailles,
          COALESCE(SUM(v.quantite), 0) as total_animaux
        FROM eleveurs e
        LEFT JOIN volailles v ON e.id = v.eleveur_id
        GROUP BY e.id, e.nom, e.prenom, e.telephone, e.email, e.adresse, e.date_creation
        ORDER BY e.date_creation DESC`,
        {
          type: QueryTypes.SELECT
        }
      );

      return res.json({
        status: 'success',
        data: eleveurs,
        total: eleveurs.length
      });
    }

    // Si l'utilisateur est éleveur, retourner ses propres informations
    if (req.user.role === 'eleveur') {
      const eleveur = await sequelize.query(
        `SELECT
          e.id,
          e.nom,
          e.prenom,
          e.telephone,
          e.email,
          e.adresse,
          e.date_creation,
          COUNT(v.id) as nombre_volailles,
          COALESCE(SUM(v.quantite), 0) as total_animaux
        FROM eleveurs e
        LEFT JOIN volailles v ON e.id = v.eleveur_id
        WHERE e.id = $1
        GROUP BY e.id, e.nom, e.prenom, e.telephone, e.email, e.adresse, e.date_creation`,
        {
          bind: [req.user.profile_id],
          type: QueryTypes.SELECT
        }
      );

      return res.json({
        status: 'success',
        data: eleveur[0] || null
      });
    }

    // Autres rôles n'ont pas accès
    return res.status(403).json({
      status: 'error',
      message: 'Accès refusé'
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des éleveurs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la récupération des éleveurs'
    });
  }
});

// @route   GET /api/eleveurs/:id/dashboard
// @desc    Get enhanced éleveur dashboard data
// @access  Private/Éleveur
router.get('/:id/dashboard', auth, isEleveur, async (req, res) => {
  try {
    const eleveurId = req.params.id;

    // Gérer le cas où profile_id est null - créer ou trouver l'éleveur
    let userEleveurId = req.user.profile_id;

    if (!userEleveurId && req.user.role === 'eleveur') {
      userEleveurId = await getOrCreateEleveurId(req.user);
    }

    // Vérifier que l'utilisateur peut accéder à ce dashboard
    if (req.user.role !== 'admin' && userEleveurId != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé à ce dashboard'
      });
    }

    // Statistiques globales améliorées
    const statsQuery = `
      SELECT
        COALESCE(SUM(v.quantite), 0) as total_volailles,
        COALESCE(SUM(CASE WHEN v.espece = 'poussins' THEN v.quantite ELSE 0 END), 0) as total_poussins,
        COALESCE(SUM(CASE WHEN v.espece = 'dindes' THEN v.quantite ELSE 0 END), 0) as total_dindes,
        COALESCE(SUM(CASE WHEN v.espece = 'poulets_chair' THEN v.quantite ELSE 0 END), 0) as total_poulets_chair,
        COALESCE(SUM(CASE WHEN v.espece = 'pondeuses' THEN v.quantite ELSE 0 END), 0) as total_pondeuses,
        COALESCE(SUM(vt.total_amount), 0) as valeur_estimee_cheptel,
        COALESCE(SUM(vt.total_amount) FILTER (WHERE vt.created_at >= NOW() - INTERVAL '30 days'), 0) as ventes_mois,
        COUNT(DISTINCT vt.id) as nombre_ventes_total,
        COUNT(DISTINCT vt.id) FILTER (WHERE vt.created_at >= NOW() - INTERVAL '30 days') as ventes_mois_count,
        COALESCE(SUM(po.quantite_oeufs), 0) as production_oeufs_total,
        COALESCE(SUM(po.quantite_oeufs) FILTER (WHERE po.date_production >= NOW() - INTERVAL '30 days'), 0) as production_oeufs_mois,
        COUNT(DISTINCT u.id) as nombre_ouvriers
      FROM eleveurs e
      LEFT JOIN volailles v ON e.id = v.eleveur_id
      LEFT JOIN ventes vt ON e.id = vt.eleveur_id
      LEFT JOIN production_oeufs po ON e.id = po.eleveur_id
      LEFT JOIN "Users" u ON u.profile_id = e.id AND u.role = 'ouvrier'
      WHERE e.id = $1
      GROUP BY e.id
    `;

    const statsResult = await sequelize.query(statsQuery, {
      replacements: [eleveurId],
      type: QueryTypes.SELECT
    });
    const stats = statsResult[0];

    // Alertes actives
    const alertesQuery = `
      SELECT
        a.*,
        CASE
          WHEN a.priorite = 'critique' THEN 1
          WHEN a.priorite = 'haute' THEN 2
          WHEN a.priorite = 'normale' THEN 3
          ELSE 4
        END as priorite_ordre
      FROM alertes_stock a
      WHERE a.eleveur_id = $1
        AND a.statut = 'active'
        AND a.visible = true
        AND a.archivee = false
      ORDER BY priorite_ordre ASC, a.date_declenchement DESC
      LIMIT 10
    `;

    // Ventes récentes
    const ventesRecentesQuery = `
      SELECT
        v.*,
        vol.espece,
        vol.race
      FROM ventes v
      LEFT JOIN volailles vol ON v.volaille_id = vol.id
      WHERE v.eleveur_id = $1
      ORDER BY v.created_at DESC
      LIMIT 10
    `;

    // Saisies quotidiennes récentes des ouvriers
    const saisiesQuotidiennesQuery = `
      SELECT
        sq.*,
        u.username as ouvrier_nom,
        f.nom as ferme_nom
      FROM saisies_quotidiennes sq
      JOIN "Users" u ON sq.ouvrier_id = u.id
      LEFT JOIN fermes f ON sq.ferme_id = f.id
      WHERE sq.eleveur_id = $1
      ORDER BY sq.date_saisie DESC, sq.created_at DESC
      LIMIT 15
    `;

    // Production d'œufs récente
    const productionOeufsQuery = `
      SELECT
        po.*,
        f.nom as ferme_nom
      FROM production_oeufs po
      LEFT JOIN fermes f ON po.ferme_id = f.id
      WHERE po.eleveur_id = $1
        AND po.date_production >= NOW() - INTERVAL '7 days'
      ORDER BY po.date_production DESC
      LIMIT 10
    `;

    // Graphiques - Évolution des stocks par espèce (30 derniers jours)
    const evolutionStocksQuery = `
      SELECT
        DATE_TRUNC('day', sq.date_saisie) as jour,
        v.espece,
        AVG(v.quantite) as quantite_moyenne,
        SUM(sq.nombre_morts) as total_morts,
        SUM(sq.nombre_malades) as total_malades
      FROM saisies_quotidiennes sq
      JOIN volailles v ON sq.volaille_id = v.id
      WHERE sq.eleveur_id = $1
        AND sq.date_saisie >= NOW() - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', sq.date_saisie), v.espece
      ORDER BY jour, v.espece
    `;

    // Tendances de production d'œufs (30 derniers jours)
    const tendancesOeufsQuery = `
      SELECT
        DATE_TRUNC('day', po.date_production) as jour,
        SUM(po.quantite_oeufs) as production_quotidienne,
        AVG(po.taux_ponte) as taux_ponte_moyen
      FROM production_oeufs po
      WHERE po.eleveur_id = $1
        AND po.date_production >= NOW() - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', po.date_production)
      ORDER BY jour
    `;

    // Évolution des ventes (6 derniers mois)
    const evolutionVentesQuery = `
      SELECT
        DATE_TRUNC('month', v.created_at) as mois,
        COUNT(*) as nombre_ventes,
        SUM(v.total_amount) as chiffre_affaires,
        AVG(v.total_amount) as vente_moyenne
      FROM ventes v
      WHERE v.eleveur_id = $1
        AND v.created_at >= NOW() - INTERVAL '6 months'
      GROUP BY DATE_TRUNC('month', v.created_at)
      ORDER BY mois
    `;

    const [
      alertes,
      ventesRecentes,
      saisiesQuotidiennes,
      productionOeufs,
      evolutionStocks,
      tendancesOeufs,
      evolutionVentes
    ] = await Promise.all([
      sequelize.query(alertesQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      }),
      sequelize.query(ventesRecentesQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      }),
      sequelize.query(saisiesQuotidiennesQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      }),
      sequelize.query(productionOeufsQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      }),
      sequelize.query(evolutionStocksQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      }),
      sequelize.query(tendancesOeufsQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      }),
      sequelize.query(evolutionVentesQuery, {
        replacements: [eleveurId],
        type: QueryTypes.SELECT
      })
    ]);

    res.json({
      status: 'success',
      data: {
        stats: {
          totalVolailles: parseInt(stats.total_volailles) || 0,
          totalPoussins: parseInt(stats.total_poussins) || 0,
          totalDindes: parseInt(stats.total_dindes) || 0,
          totalPouletsChair: parseInt(stats.total_poulets_chair) || 0,
          totalPondeuses: parseInt(stats.total_pondeuses) || 0,
          valeurEstimeeCheptel: parseFloat(stats.valeur_estimee_cheptel) || 0,
          ventesMois: parseFloat(stats.ventes_mois) || 0,
          nombreVentesTotal: parseInt(stats.nombre_ventes_total) || 0,
          ventesMoisCount: parseInt(stats.ventes_mois_count) || 0,
          productionOeufsTotal: parseInt(stats.production_oeufs_total) || 0,
          productionOeufsMois: parseInt(stats.production_oeufs_mois) || 0,
          nombreOuvriers: parseInt(stats.nombre_ouvriers) || 0
        },
        alertes,
        ventesRecentes,
        saisiesQuotidiennes,
        productionOeufs,
        graphiques: {
          evolutionStocks,
          tendancesOeufs,
          evolutionVentes
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du dashboard éleveur:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la récupération du dashboard'
    });
  }
});

// @route   GET /api/eleveurs/:id/ouvriers
// @desc    Get farm workers (ouvriers) for an éleveur
// @access  Private/Éleveur
router.get('/:id/ouvriers', auth, isEleveur, async (req, res) => {
  try {
    const eleveurId = req.params.id;

    if (req.user.role !== 'admin' && req.user.profile_id != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé'
      });
    }

    const ouvriersQuery = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.first_name,
        u.last_name,
        u.status,
        u.createdAt,
        COUNT(DISTINCT sq.id) as nombre_saisies,
        MAX(sq.date_saisie) as derniere_saisie,
        COUNT(DISTINCT fo.ferme_id) as fermes_assignees
      FROM "Users" u
      LEFT JOIN saisies_quotidiennes sq ON u.id = sq.ouvrier_id
      LEFT JOIN fermes_ouvriers fo ON u.id = fo.ouvrier_id
      WHERE u.profile_id = $1 AND u.role = 'ouvrier'
      GROUP BY u.id, u.username, u.email, u.first_name, u.last_name, u.status, u.createdAt
      ORDER BY u.createdAt DESC
    `;

    const ouvriers = await sequelize.query(ouvriersQuery, {
      replacements: [eleveurId],
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: {
        ouvriers
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des ouvriers:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur'
    });
  }
});

// @route   POST /api/eleveurs/:id/ouvriers
// @desc    Create new farm worker (ouvrier)
// @access  Private/Éleveur
router.post('/:id/ouvriers', auth, isEleveur, [
  body('username').notEmpty().withMessage('Le nom d\'utilisateur est requis'),
  body('email').isEmail().withMessage('Email invalide'),
  body('password').isLength({ min: 6 }).withMessage('Le mot de passe doit contenir au moins 6 caractères'),
  body('first_name').optional().isString(),
  body('last_name').optional().isString(),
  body('fermes').optional().isArray().withMessage('Les fermes doivent être un tableau')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const eleveurId = req.params.id;
    const { username, email, password, first_name, last_name, fermes = [] } = req.body;

    if (req.user.role !== 'admin' && req.user.profile_id != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé'
      });
    }

    // Vérifier si l'email ou le username existe déjà
    const existingUserQuery = `
      SELECT id FROM "Users"
      WHERE email = $1 OR username = $2
    `;

    const existingUser = await sequelize.query(existingUserQuery, {
      replacements: [email, username],
      type: QueryTypes.SELECT
    });

    if (existingUser.length > 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Un utilisateur avec cet email ou nom d\'utilisateur existe déjà'
      });
    }

    // Hasher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 10);

    // Créer l'ouvrier
    const createUserQuery = `
      INSERT INTO "Users" (
        username, email, password, role, first_name, last_name,
        profile_id, status, createdAt, updatedAt
      ) VALUES ($1, $2, $3, 'ouvrier', $4, $5, $6, 'active', NOW(), NOW())
      RETURNING id, username, email, first_name, last_name, status, createdAt
    `;

    const newUser = await sequelize.query(createUserQuery, {
      replacements: [username, email, hashedPassword, first_name, last_name, eleveurId],
      type: QueryTypes.INSERT
    });

    const ouvrierId = newUser[0][0].id;

    // Assigner les fermes si spécifiées
    if (fermes.length > 0) {
      const fermeAssignments = fermes.map(fermeId =>
        `(${ouvrierId}, ${fermeId}, NOW(), NOW())`
      ).join(', ');

      const assignFermesQuery = `
        INSERT INTO fermes_ouvriers (ouvrier_id, ferme_id, createdAt, updatedAt)
        VALUES ${fermeAssignments}
      `;

      await sequelize.query(assignFermesQuery, {
        type: QueryTypes.INSERT
      });
    }

    res.status(201).json({
      status: 'success',
      message: 'Ouvrier créé avec succès',
      data: {
        ouvrier: newUser[0][0],
        fermes_assignees: fermes
      }
    });

  } catch (error) {
    console.error('Erreur lors de la création de l\'ouvrier:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la création de l\'ouvrier'
    });
  }
});

// @route   POST /api/eleveurs/saisies-quotidiennes
// @desc    Create daily data entry (for farm workers)
// @access  Private/Ouvrier
router.post('/saisies-quotidiennes', auth, isOuvrier, [
  body('eleveur_id').isInt().withMessage('ID éleveur requis'),
  body('ferme_id').optional().isInt(),
  body('volaille_id').optional().isInt(),
  body('date_saisie').isISO8601().withMessage('Date de saisie invalide'),
  body('nombre_morts').isInt({ min: 0 }).withMessage('Nombre de morts invalide'),
  body('nombre_malades').optional().isInt({ min: 0 }),
  body('temperature_moyenne').optional().isFloat(),
  body('humidite_moyenne').optional().isFloat(),
  body('consommation_eau').optional().isFloat({ min: 0 }),
  body('consommation_aliment').optional().isFloat({ min: 0 }),
  body('incidents').optional().isString(),
  body('besoins_materiels').optional().isString(),
  body('observations').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const {
      eleveur_id,
      ferme_id,
      volaille_id,
      date_saisie,
      nombre_morts,
      nombre_malades = 0,
      temperature_moyenne,
      humidite_moyenne,
      consommation_eau,
      consommation_aliment,
      incidents,
      besoins_materiels,
      observations
    } = req.body;

    const ouvrierId = req.user.id;

    // Vérifier que l'ouvrier peut saisir pour cet éleveur
    if (req.user.role === 'ouvrier' && req.user.profile_id != eleveur_id) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé pour cet éleveur'
      });
    }

    const insertQuery = `
      INSERT INTO saisies_quotidiennes (
        eleveur_id, ouvrier_id, ferme_id, volaille_id, date_saisie,
        nombre_morts, nombre_malades, temperature_moyenne, humidite_moyenne,
        consommation_eau, consommation_aliment, incidents, besoins_materiels,
        observations, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW())
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: [
        eleveur_id, ouvrierId, ferme_id, volaille_id, date_saisie,
        nombre_morts, nombre_malades, temperature_moyenne, humidite_moyenne,
        consommation_eau, consommation_aliment, incidents, besoins_materiels,
        observations
      ],
      type: QueryTypes.INSERT
    });

    // Créer des alertes automatiques si nécessaire
    if (nombre_morts > 10) {
      const alerteQuery = `
        INSERT INTO alertes_stock (
          eleveur_id, type_alerte, priorite, titre, message,
          donnees_contexte, date_declenchement, created_at, updated_at
        ) VALUES ($1, 'mortalite_elevee', 'haute', 'Mortalité élevée détectée',
                 'Mortalité de $2 animaux signalée le $3',
                 $4, NOW(), NOW(), NOW())
      `;

      await sequelize.query(alerteQuery, {
        replacements: [
          eleveur_id,
          nombre_morts,
          date_saisie,
          JSON.stringify({
            ferme_id,
            volaille_id,
            ouvrier_id: ouvrierId,
            nombre_morts,
            nombre_malades
          })
        ],
        type: QueryTypes.INSERT
      });
    }

    res.status(201).json({
      status: 'success',
      message: 'Saisie quotidienne enregistrée avec succès',
      data: {
        saisie: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la saisie quotidienne:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la saisie'
    });
  }
});

// @route   POST /api/eleveurs/:id/ventes/quick
// @desc    Quick action - Add new sale
// @access  Private/Éleveur
router.post('/:id/ventes/quick', auth, isEleveur, [
  body('volaille_id').isInt().withMessage('ID volaille requis'),
  body('quantite').isInt({ min: 1 }).withMessage('Quantité invalide'),
  body('prix_unitaire').isFloat({ min: 0 }).withMessage('Prix unitaire invalide'),
  body('acheteur').notEmpty().withMessage('Nom de l\'acheteur requis'),
  body('date_vente').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const eleveurId = req.params.id;
    const { volaille_id, quantite, prix_unitaire, acheteur, date_vente = new Date() } = req.body;

    if (req.user.role !== 'admin' && req.user.profile_id != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé'
      });
    }

    const total_amount = quantite * prix_unitaire;

    const insertQuery = `
      INSERT INTO ventes (
        eleveur_id, volaille_id, quantite, prix_unitaire, total_amount,
        acheteur, date_vente, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'confirmee', NOW(), NOW())
      RETURNING *
    `;

    const result = await sequelize.query(insertQuery, {
      replacements: [eleveurId, volaille_id, quantite, prix_unitaire, total_amount, acheteur, date_vente],
      type: QueryTypes.INSERT
    });

    // Mettre à jour le stock de volailles
    const updateStockQuery = `
      UPDATE volailles
      SET quantite = GREATEST(0, quantite - $1), updated_at = NOW()
      WHERE id = $2 AND eleveur_id = $3
    `;

    await sequelize.query(updateStockQuery, {
      replacements: [quantite, volaille_id, eleveurId],
      type: QueryTypes.UPDATE
    });

    res.status(201).json({
      status: 'success',
      message: 'Vente enregistrée avec succès',
      data: {
        vente: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement de la vente:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de l\'enregistrement de la vente'
    });
  }
});

// @route   GET /api/eleveurs/:id/activites
// @desc    Get multi-activity view for éleveur
// @access  Private/Éleveur
router.get('/:id/activites', auth, isEleveur, async (req, res) => {
  try {
    const eleveurId = req.params.id;
    const { type_activite } = req.query;

    if (req.user.role !== 'admin' && req.user.profile_id != eleveurId) {
      return res.status(403).json({
        status: 'error',
        message: 'Accès refusé'
      });
    }

    let whereClause = 'WHERE v.eleveur_id = $1';
    let replacements = [eleveurId];

    if (type_activite) {
      whereClause += ' AND v.espece = $2';
      replacements.push(type_activite);
    }

    const activitesQuery = `
      SELECT
        v.espece as type_activite,
        v.race,
        SUM(v.quantite) as stock_total,
        AVG(v.age_semaines) as age_moyen,
        COUNT(DISTINCT v.id) as nombre_lots,
        COALESCE(SUM(vt.total_amount), 0) as revenus_totaux,
        COALESCE(SUM(vt.total_amount) FILTER (WHERE vt.created_at >= NOW() - INTERVAL '30 days'), 0) as revenus_mois,
        COUNT(DISTINCT sq.id) as nombre_saisies_quotidiennes,
        COALESCE(SUM(sq.nombre_morts), 0) as total_morts,
        COALESCE(SUM(po.quantite_oeufs), 0) as production_oeufs
      FROM volailles v
      LEFT JOIN ventes vt ON v.id = vt.volaille_id
      LEFT JOIN saisies_quotidiennes sq ON v.id = sq.volaille_id
      LEFT JOIN production_oeufs po ON v.id = po.volaille_id
      ${whereClause}
      GROUP BY v.espece, v.race
      ORDER BY stock_total DESC
    `;

    const activites = await sequelize.query(activitesQuery, {
      replacements,
      type: QueryTypes.SELECT
    });

    res.json({
      status: 'success',
      data: {
        activites,
        filtre_applique: type_activite || 'toutes'
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des activités:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur'
    });
  }
});

// @route   GET /api/eleveur/ventes
// @desc    Get sales for current éleveur
// @access  Private/Éleveur
router.get('/ventes', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    let eleveurId = req.user.profile_id;

    // Gérer le cas où profile_id est null
    if (!eleveurId && req.user.role === 'eleveur') {
      eleveurId = await getOrCreateEleveurId(req.user);
    }

    if (!eleveurId && req.user.role !== 'admin') {
      return res.status(400).json({
        status: 'error',
        message: 'Profile ID manquant pour l\'éleveur'
      });
    }

    const ventes = await sequelize.query(
      `SELECT
        v.id,
        v.date_vente,
        v.quantite,
        v.prix_unitaire,
        v.prix_total,
        v.acheteur_nom,
        v.acheteur_contact,
        vol.type_volaille,
        vol.race,
        vol.age_semaines
      FROM ventes v
      LEFT JOIN volailles vol ON v.volaille_id = vol.id
      WHERE vol.eleveur_id = $1
      ORDER BY v.date_vente DESC`,
      {
        bind: [eleveurId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: ventes,
      count: ventes.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des ventes:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des ventes'
    });
  }
});

// @route   GET /api/eleveur/ventes/stats
// @desc    Get sales statistics for current éleveur
// @access  Private/Éleveur
router.get('/ventes/stats', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    let eleveurId = req.user.profile_id;

    // Gérer le cas où profile_id est null
    if (!eleveurId && req.user.role === 'eleveur') {
      eleveurId = await getOrCreateEleveurId(req.user);
    }

    if (!eleveurId && req.user.role !== 'admin') {
      return res.status(400).json({
        status: 'error',
        message: 'Profile ID manquant pour l\'éleveur'
      });
    }

    const stats = await sequelize.query(
      `SELECT
        COUNT(*) as total_ventes,
        COALESCE(SUM(prix_total), 0) as chiffre_affaires_total,
        COALESCE(AVG(prix_total), 0) as prix_moyen,
        COALESCE(SUM(quantite), 0) as quantite_totale
      FROM ventes v
      LEFT JOIN volailles vol ON v.volaille_id = vol.id
      WHERE vol.eleveur_id = $1`,
      {
        bind: [eleveurId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: stats[0]
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de ventes:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des statistiques de ventes'
    });
  }
});

// @route   GET /api/eleveur/statistics/detailed
// @desc    Get detailed statistics for current éleveur
// @access  Private/Éleveur
router.get('/statistics/detailed', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    let eleveurId = req.user.profile_id;
    const { period = '6months' } = req.query;

    // Gérer le cas où profile_id est null
    if (!eleveurId && req.user.role === 'eleveur') {
      eleveurId = await getOrCreateEleveurId(req.user);
    }

    if (!eleveurId && req.user.role !== 'admin') {
      return res.status(400).json({
        status: 'error',
        message: 'Profile ID manquant pour l\'éleveur'
      });
    }

    // Calculate date range based on period
    let dateFilter = '';
    switch (period) {
      case '1month':
        dateFilter = "AND v.date_vente >= NOW() - INTERVAL '1 month'";
        break;
      case '3months':
        dateFilter = "AND v.date_vente >= NOW() - INTERVAL '3 months'";
        break;
      case '6months':
        dateFilter = "AND v.date_vente >= NOW() - INTERVAL '6 months'";
        break;
      case '1year':
        dateFilter = "AND v.date_vente >= NOW() - INTERVAL '1 year'";
        break;
      default:
        dateFilter = "AND v.date_vente >= NOW() - INTERVAL '6 months'";
    }

    const stats = await sequelize.query(
      `SELECT
        COUNT(DISTINCT vol.id) as total_volailles,
        COUNT(DISTINCT v.id) as total_ventes,
        COALESCE(SUM(v.prix_total), 0) as chiffre_affaires,
        COALESCE(AVG(v.prix_total), 0) as prix_moyen_vente,
        COUNT(DISTINCT CASE WHEN vol.type_volaille = 'pondeuse' THEN vol.id END) as pondeuses,
        COUNT(DISTINCT CASE WHEN vol.type_volaille = 'chair' THEN vol.id END) as chairs,
        COUNT(DISTINCT CASE WHEN vol.type_volaille = 'dinde' THEN vol.id END) as dindes
      FROM volailles vol
      LEFT JOIN ventes v ON vol.id = v.volaille_id ${dateFilter}
      WHERE vol.eleveur_id = $1`,
      {
        bind: [eleveurId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: stats[0],
      period
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques détaillées:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des statistiques détaillées'
    });
  }
});

// @route   GET /api/eleveur/statistics/kpis
// @desc    Get KPI statistics for current éleveur
// @access  Private/Éleveur
router.get('/statistics/kpis', auth, checkRole(['eleveur', 'admin']), async (req, res) => {
  try {
    let eleveurId = req.user.profile_id;
    const { period = '6months' } = req.query;

    // Gérer le cas où profile_id est null
    if (!eleveurId && req.user.role === 'eleveur') {
      eleveurId = await getOrCreateEleveurId(req.user);
    }

    if (!eleveurId && req.user.role !== 'admin') {
      return res.status(400).json({
        status: 'error',
        message: 'Profile ID manquant pour l\'éleveur'
      });
    }

    // Mock KPI data - replace with actual calculations
    const kpis = {
      taux_mortalite: 2.5,
      taux_ponte: 85.3,
      consommation_aliment: 125.7,
      gain_poids_moyen: 2.1,
      rentabilite: 15.8,
      efficacite_alimentaire: 1.8
    };

    res.json({
      status: 'success',
      data: kpis,
      period
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des KPIs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des KPIs'
    });
  }
});

module.exports = router;
