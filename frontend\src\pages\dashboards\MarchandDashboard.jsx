import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fab,
  Tab,
  Tabs,
} from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import {
  Store as StoreIcon,
  ShoppingCart as CartIcon,
  AttachMoney as MoneyIcon,
  Inventory as InventoryIcon,
  LocalShipping as ShippingIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Star as StarIcon,
  Psychology as PsychologyIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';

// Import Marchand-specific components
import MarketplaceManagement from '../../components/dashboards/marchand/MarketplaceManagement';
import OrderManagement from '../../components/dashboards/marchand/OrderManagement';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

// Couleurs pour les graphiques
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Composant pour afficher une carte de statistique
const StatCard = ({ title, value, icon, color, subtitle, trend }) => {
  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar sx={{ bgcolor: color, mr: 2 }}>
          {icon}
        </Avatar>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h4" component="div" fontWeight="bold" color={color}>
            {typeof value === 'number' && value > 1000 ?
              `${(value / 1000).toFixed(1)}k` :
              value
            }
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="caption" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        {trend && (
          <Box sx={{ textAlign: 'right' }}>
            <Typography
              variant="caption"
              color={trend > 0 ? 'success.main' : 'error.main'}
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
              {trend > 0 ? '+' : ''}{trend}%
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

// Composant pour les actions rapides
const QuickActionDialog = ({ open, onClose, type, onSubmit }) => {
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await onSubmit(formData);
      setFormData({});
      onClose();
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderForm = () => {
    if (type === 'product') {
      return (
        <>
          <TextField
            fullWidth
            label="Nom du produit"
            value={formData.name || ''}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Description"
            multiline
            rows={3}
            value={formData.description || ''}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Prix"
            type="number"
            value={formData.price || ''}
            onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value)})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Quantité en stock"
            type="number"
            value={formData.stock_quantity || ''}
            onChange={(e) => setFormData({...formData, stock_quantity: parseInt(e.target.value)})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Catégorie"
            value={formData.category || ''}
            onChange={(e) => setFormData({...formData, category: e.target.value})}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Seuil d'alerte stock"
            type="number"
            value={formData.stock_alert_threshold || 10}
            onChange={(e) => setFormData({...formData, stock_alert_threshold: parseInt(e.target.value)})}
            margin="normal"
          />
        </>
      );
    } else if (type === 'stock') {
      return (
        <>
          <TextField
            fullWidth
            label="Quantité"
            type="number"
            value={formData.stock_quantity || ''}
            onChange={(e) => setFormData({...formData, stock_quantity: parseInt(e.target.value)})}
            margin="normal"
            required
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>Opération</InputLabel>
            <Select
              value={formData.operation || 'set'}
              onChange={(e) => setFormData({...formData, operation: e.target.value})}
            >
              <MenuItem value="set">Définir</MenuItem>
              <MenuItem value="add">Ajouter</MenuItem>
              <MenuItem value="subtract">Soustraire</MenuItem>
            </Select>
          </FormControl>
        </>
      );
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {type === 'product' ? 'Ajouter un produit' : 'Mettre à jour le stock'}
      </DialogTitle>
      <DialogContent>
        {renderForm()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Annuler</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
        >
          {loading ? <CircularProgress size={20} /> : 'Enregistrer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Composant pour les recommandations IA
const AIRecommendations = ({ recommendations }) => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <PsychologyIcon sx={{ mr: 1, color: 'primary.main' }} />
        Recommandations IA
      </Typography>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Prix" />
        <Tab label="Stock" />
        <Tab label="Tendances" />
      </Tabs>

      <Box sx={{ height: '300px', overflow: 'auto' }}>
        {tabValue === 0 && (
          <List>
            {recommendations.price_optimization?.map((rec, index) => (
              <ListItem key={index} divider>
                <ListItemText
                  primary={rec.product_name}
                  secondary={
                    <Box>
                      <Typography variant="body2">
                        Prix actuel: {rec.current_price}€ → Suggéré: {rec.suggested_price}€
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {rec.reason}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}

        {tabValue === 1 && (
          <List>
            {recommendations.inventory_suggestions?.map((rec, index) => (
              <ListItem key={index} divider>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: rec.action === 'restock' ? 'warning.main' : 'info.main' }}>
                    <InventoryIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={rec.product_name}
                  secondary={
                    <Box>
                      <Typography variant="body2">
                        Action: {rec.action === 'restock' ? 'Réapprovisionner' : 'Réduire le stock'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {rec.reason}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}

        {tabValue === 2 && (
          <List>
            {recommendations.market_trends?.map((trend, index) => (
              <ListItem key={index} divider>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <TrendingUpIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={trend.trend}
                  secondary={
                    <Typography variant="caption" color="text.secondary">
                      Confiance: {(trend.confidence * 100).toFixed(0)}%
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Paper>
  );
};

const MarchandDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [recommendations, setRecommendations] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quickActionDialog, setQuickActionDialog] = useState({ open: false, type: null, productId: null });
  const [refreshing, setRefreshing] = useState(false);
  const [mainTabValue, setMainTabValue] = useState(0);

  // Fonction pour récupérer les données du dashboard
  const fetchDashboardData = async () => {
    try {
      setRefreshing(true);
      const token = localStorage.getItem('token');

      const [dashboardResponse, recommendationsResponse] = await Promise.all([
        axios.get('/api/marchand/dashboard', {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get('/api/marchand/ai/recommendations', {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      setDashboardData(dashboardResponse.data.data);
      setRecommendations(recommendationsResponse.data.data.recommendations);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la récupération des données:', err);
      setError('Erreur lors du chargement des données du dashboard');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fonction pour les actions rapides
  const handleQuickAction = async (type, data) => {
    try {
      const token = localStorage.getItem('token');

      if (type === 'product') {
        await axios.post('/api/marchand/products/quick', data, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else if (type === 'stock') {
        await axios.patch(`/api/marchand/products/${quickActionDialog.productId}/stock`, data, {
          headers: { Authorization: `Bearer ${token}` }
        });
      }

      // Rafraîchir les données
      fetchDashboardData();
    } catch (error) {
      console.error('Erreur lors de l\'action rapide:', error);
      throw error;
    }
  };

  useEffect(() => {
    fetchDashboardData();

    // Rafraîchir les données toutes les 5 minutes
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={fetchDashboardData}>
          Réessayer
        </Button>
      </Container>
    );
  }

  const { stats, commandesRecentes, produitsPopulaires, alertesStock, graphiques } = dashboardData;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* En-tête avec actions rapides */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard Marchand
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Badge badgeContent={alertesStock.length} color="error">
            <IconButton color="warning">
              <WarningIcon />
            </IconButton>
          </Badge>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => setQuickActionDialog({ open: true, type: 'product' })}
          >
            Nouveau produit
          </Button>
          <Button
            variant="contained"
            startIcon={<AnalyticsIcon />}
            onClick={fetchDashboardData}
            disabled={refreshing}
          >
            {refreshing ? <CircularProgress size={20} /> : 'Actualiser'}
          </Button>
        </Box>
      </Box>

      {/* Onglets pour les différentes vues */}
      <Paper elevation={2} sx={{ mb: 4 }}>
        <Tabs value={mainTabValue} onChange={(e, newValue) => setMainTabValue(newValue)} variant="scrollable" scrollButtons="auto">
          <Tab label="Vue d'ensemble" />
          <Tab label="Gestion Produits" />
          <Tab label="Gestion Commandes" />
          <Tab label="Analytics" />
        </Tabs>
      </Paper>

      {/* Contenu des onglets */}
      {mainTabValue === 0 && (
        <>
          {/* Cartes de statistiques */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Produits"
            value={stats.totalProduits}
            subtitle={`+${stats.produitsAjoutesMois} ce mois`}
            icon={<StoreIcon />}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Commandes"
            value={stats.totalCommandes}
            subtitle={`+${stats.commandesMois} ce mois`}
            icon={<CartIcon />}
            color="#2e7d32"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Chiffre d'Affaires"
            value={`${stats.chiffreAffairesTotal.toFixed(0)}€`}
            subtitle={`+${stats.chiffreAffairesMois.toFixed(0)}€ ce mois`}
            icon={<MoneyIcon />}
            color="#ed6c02"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Note Moyenne"
            value={stats.noteMoyenne.toFixed(1)}
            subtitle={`${stats.commandesEnAttente} en attente`}
            icon={<StarIcon />}
            color="#9c27b0"
          />
        </Grid>
      </Grid>

      {/* Alertes de stock */}
      {alertesStock && alertesStock.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
                Alertes de Stock
              </Typography>
              <Grid container spacing={2}>
                {alertesStock.slice(0, 4).map((produit) => (
                  <Grid item xs={12} sm={6} md={3} key={produit.id}>
                    <Card
                      sx={{
                        border: `2px solid ${produit.niveau_alerte === 'rupture' ? '#f44336' : '#ff9800'}`,
                        bgcolor: produit.niveau_alerte === 'rupture' ? '#ffebee' : '#fff3e0'
                      }}
                    >
                      <CardContent>
                        <Typography variant="h6" component="div" noWrap>
                          {produit.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Stock: {produit.stock_quantity} / {produit.stock_alert_threshold}
                        </Typography>
                        <Chip
                          label={produit.niveau_alerte.toUpperCase()}
                          color={produit.niveau_alerte === 'rupture' ? 'error' : 'warning'}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                        <Button
                          size="small"
                          variant="outlined"
                          sx={{ mt: 1, ml: 1 }}
                          onClick={() => setQuickActionDialog({
                            open: true,
                            type: 'stock',
                            productId: produit.id
                          })}
                        >
                          Réapprovisionner
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      )}

      <Grid container spacing={3}>
        {/* Commandes récentes */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <CartIcon sx={{ mr: 1 }} />
              Commandes Récentes
            </Typography>
            <List>
              {commandesRecentes.map((commande) => (
                <ListItem key={commande.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <CartIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={`Commande #${commande.order_number || commande.id}`}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Client: {commande.client_nom || 'Client anonyme'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Montant: {commande.total_amount}€ - {commande.nombre_articles} articles
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(commande.created_at).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                  />
                  <Chip
                    label={commande.status}
                    color={commande.status === 'delivered' ? 'success' : 'primary'}
                    size="small"
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Produits populaires */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUpIcon sx={{ mr: 1 }} />
              Produits Populaires
            </Typography>
            <List>
              {produitsPopulaires.map((produit) => (
                <ListItem key={produit.id} divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <StoreIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={produit.name}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Vendu: {produit.quantite_vendue} unités
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Revenus: {produit.revenus_generes}€
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {produit.nombre_commandes} commandes
                        </Typography>
                      </Box>
                    }
                  />
                  <IconButton size="small">
                    <VisibilityIcon />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Graphique des ventes */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              Évolution des Ventes (30 jours)
            </Typography>
            <ResponsiveContainer width="100%" height="85%">
              <AreaChart data={graphiques.ventesParJour}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="jour"
                  tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })}
                />
                <YAxis />
                <ChartTooltip
                  labelFormatter={(value) => new Date(value).toLocaleDateString('fr-FR')}
                  formatter={(value, name) => [
                    name === 'chiffre_affaires' ? `${value}€` : value,
                    name === 'chiffre_affaires' ? 'Chiffre d\'affaires' : 'Commandes'
                  ]}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="chiffre_affaires"
                  stroke="#1976d2"
                  fill="#1976d2"
                  fillOpacity={0.3}
                  name="Chiffre d'affaires"
                />
                <Bar
                  dataKey="nombre_commandes"
                  fill="#2e7d32"
                  name="Nombre de commandes"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Recommandations IA */}
        <Grid item xs={12} md={6}>
          <AIRecommendations recommendations={recommendations} />
        </Grid>
      </Grid>
        </>
      )}

      {/* Tab 1: Gestion Produits */}
      {mainTabValue === 1 && (
        <MarketplaceManagement />
      )}

      {/* Tab 2: Gestion Commandes */}
      {mainTabValue === 2 && (
        <OrderManagement />
      )}

      {/* Tab 3: Analytics */}
      {mainTabValue === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Analytics Avancées
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Fonctionnalités d'analytics avancées à venir...
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Bouton de rafraîchissement flottant */}
      <Fab
        color="primary"
        aria-label="refresh"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={fetchDashboardData}
        disabled={refreshing}
      >
        {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
      </Fab>

      {/* Dialog pour les actions rapides */}
      <QuickActionDialog
        open={quickActionDialog.open}
        type={quickActionDialog.type}
        onClose={() => setQuickActionDialog({ open: false, type: null, productId: null })}
        onSubmit={(data) => handleQuickAction(quickActionDialog.type, data)}
      />
    </Container>
  );
};

export default MarchandDashboard;
