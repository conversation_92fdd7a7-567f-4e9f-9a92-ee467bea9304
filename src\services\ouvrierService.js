const { Op } = require('sequelize');
const Ouvrier = require('../models/ouvrier');
const Eleveur = require('../models/eleveur');

class OuvrierService {
  static async getAllOuvriers(filters = {}) {
    try {
      const where = {};

      if (filters.eleveur_id) {
        where.eleveur_id = filters.eleveur_id;
      }

      if (filters.statut) {
        where.statut = filters.statut;
      }

      if (filters.search) {
        where[Op.or] = [
          { nom: { [Op.iLike]: `%${filters.search}%` } },
          { prenom: { [Op.iLike]: `%${filters.search}%` } },
          { telephone: { [Op.iLike]: `%${filters.search}%` } }
        ];
      }

      const ouvriers = await Ouvrier.findAll({
        where,
        include: [{
          model: Eleveur,
          as: 'eleveur',
          attributes: ['nom', 'prenom', 'email']
        }],
        order: [['createdAt', 'DESC']]
      });

      return ouvriers;
    } catch (error) {
      console.error('Erreur dans getAllOuvriers:', error);
      throw error;
    }
  }

  static async getOuvrierById(id) {
    try {
      const ouvrier = await Ouvrier.findByPk(id, {
        include: [{
          model: Eleveur,
          as: 'eleveur',
          attributes: ['nom', 'prenom', 'email']
        }]
      });

      if (!ouvrier) {
        throw new Error('Ouvrier non trouvé');
      }

      return ouvrier;
    } catch (error) {
      console.error('Erreur dans getOuvrierById:', error);
      throw error;
    }
  }

  static async createOuvrier(data) {
    try {
      // Vérifier si l'éleveur existe
      const eleveur = await Eleveur.findByPk(data.eleveur_id);
      if (!eleveur) {
        throw new Error('Éleveur non trouvé');
      }

      const ouvrier = await Ouvrier.create(data);
      return await this.getOuvrierById(ouvrier.id);
    } catch (error) {
      console.error('Erreur dans createOuvrier:', error);
      throw error;
    }
  }

  static async updateOuvrier(id, data) {
    try {
      const ouvrier = await Ouvrier.findByPk(id);
      if (!ouvrier) {
        throw new Error('Ouvrier non trouvé');
      }

      if (data.eleveur_id) {
        const eleveur = await Eleveur.findByPk(data.eleveur_id);
        if (!eleveur) {
          throw new Error('Éleveur non trouvé');
        }
      }

      await ouvrier.update(data);
      return await this.getOuvrierById(id);
    } catch (error) {
      console.error('Erreur dans updateOuvrier:', error);
      throw error;
    }
  }

  static async deleteOuvrier(id) {
    try {
      const ouvrier = await Ouvrier.findByPk(id);
      if (!ouvrier) {
        throw new Error('Ouvrier non trouvé');
      }

      await ouvrier.destroy();
      return true;
    } catch (error) {
      console.error('Erreur dans deleteOuvrier:', error);
      throw error;
    }
  }

  static async getOuvriersByEleveur(eleveurId) {
    try {
      const ouvriers = await Ouvrier.findByEleveur(eleveurId);
      return ouvriers;
    } catch (error) {
      console.error('Erreur dans getOuvriersByEleveur:', error);
      throw error;
    }
  }

  static async updateOuvrierStatus(id, statut) {
    try {
      const ouvrier = await Ouvrier.findByPk(id);
      if (!ouvrier) {
        throw new Error('Ouvrier non trouvé');
      }

      if (!['actif', 'inactif'].includes(statut)) {
        throw new Error('Statut invalide');
      }

      await ouvrier.update({ statut });
      return await this.getOuvrierById(id);
    } catch (error) {
      console.error('Erreur dans updateOuvrierStatus:', error);
      throw error;
    }
  }
}

module.exports = OuvrierService;