require('dotenv').config();
const { sequelize } = require('../models');

beforeAll(async () => {
  // Réinitialiser la base de données avant les tests
  await sequelize.sync({ force: true });
});

afterAll(async () => {
  // Fermer la connexion à la base de données après les tests
  await sequelize.close();
});

// Augmenter le timeout pour les tests
jest.setTimeout(30000);

// Désactiver les logs pendant les tests
console.log = jest.fn();
console.error = jest.fn();
console.warn = jest.fn();

// Helper pour créer un mock de req, res, next
global.mockExpressHandler = () => {
  const req = {
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null
  };
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis()
  };
  const next = jest.fn();
  return { req, res, next };
};