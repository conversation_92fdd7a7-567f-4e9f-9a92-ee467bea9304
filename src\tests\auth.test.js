const request = require('supertest');
const app = require('../index');
const { User, Role, Eleveur, Ouvrier } = require('../models');
const AuthService = require('../services/authService');
const bcrypt = require('bcryptjs');

describe('Authentication Service Tests', () => {
  let adminRole, eleveurRole, ouvrierRole;

  beforeAll(async () => {
    // Créer les rôles nécessaires
    adminRole = await Role.create({
      name: 'admin',
      description: 'Admin role',
      permissions: ['manage_users', 'manage_ouvriers']
    });

    eleveurRole = await Role.create({
      name: 'eleveur',
      description: 'Eleveur role',
      permissions: ['manage_own_ouvriers']
    });

    ouvrierRole = await Role.create({
      name: 'ouvrier',
      description: 'Ouvrier role',
      permissions: ['view_own_profile']
    });
  });

  afterAll(async () => {
    // Nettoyer la base de données
    await User.destroy({ where: {} });
    await Role.destroy({ where: {} });
    await Eleveur.destroy({ where: {} });
    await Ouvrier.destroy({ where: {} });
  });

  describe('createUser', () => {
    it('should create a new user with role', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Test123!',
        first_name: 'Test',
        last_name: 'User'
      };

      const user = await AuthService.createUser(userData, 'admin');
      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.role_id).toBe(adminRole.id);
    });

    it('should not create user with duplicate email', async () => {
      const userData = {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'Test123!',
        first_name: 'Test',
        last_name: 'User'
      };

      await expect(AuthService.createUser(userData, 'admin'))
        .rejects
        .toThrow('Cet email est déjà utilisé');
    });
  });

  describe('createOuvrierWithAccount', () => {
    let eleveur;

    beforeAll(async () => {
      eleveur = await Eleveur.create({
        nom: 'Test',
        prenom: 'Eleveur',
        email: '<EMAIL>',
        telephone: '**********',
        adresse: 'Test address'
      });
    });

    it('should create ouvrier with user account', async () => {
      const ouvrierData = {
        nom: 'Test',
        prenom: 'Ouvrier',
        email: '<EMAIL>',
        password: 'Ouvrier123!',
        telephone: '**********',
        adresse: 'Test address',
        date_embauche: new Date(),
        salaire: 30000
      };

      const result = await AuthService.createOuvrierWithAccount(ouvrierData, eleveur.id);
      expect(result.user).toBeDefined();
      expect(result.ouvrier).toBeDefined();
      expect(result.ouvrier.eleveur_id).toBe(eleveur.id);
      expect(result.user.role_id).toBe(ouvrierRole.id);
    });

    it('should not create ouvrier with invalid eleveur', async () => {
      const ouvrierData = {
        nom: 'Test2',
        prenom: 'Ouvrier2',
        email: '<EMAIL>',
        password: 'Ouvrier123!',
        telephone: '**********',
        adresse: 'Test address 2',
        date_embauche: new Date(),
        salaire: 35000
      };

      await expect(AuthService.createOuvrierWithAccount(ouvrierData, 999999))
        .rejects
        .toThrow('Éleveur non trouvé');
    });
  });

  describe('login', () => {
    let testUser;

    beforeAll(async () => {
      testUser = await AuthService.createUser({
        username: 'logintest',
        email: '<EMAIL>',
        password: 'Login123!',
        first_name: 'Login',
        last_name: 'Test'
      }, 'admin');
    });

    it('should login with valid credentials', async () => {
      const result = await AuthService.login('<EMAIL>', 'Login123!');
      expect(result.token).toBeDefined();
      expect(result.user).toBeDefined();
      expect(result.user.email).toBe('<EMAIL>');
    });

    it('should not login with invalid password', async () => {
      await expect(AuthService.login('<EMAIL>', 'WrongPassword123!'))
        .rejects
        .toThrow('Identifiants invalides');
    });

    it('should not login with non-existent email', async () => {
      await expect(AuthService.login('<EMAIL>', 'Login123!'))
        .rejects
        .toThrow('Identifiants invalides');
    });
  });

  describe('changePassword', () => {
    let testUser;

    beforeAll(async () => {
      testUser = await AuthService.createUser({
        username: 'passwordtest',
        email: '<EMAIL>',
        password: 'Password123!',
        first_name: 'Password',
        last_name: 'Test'
      }, 'admin');
    });

    it('should change password with valid old password', async () => {
      const result = await AuthService.changePassword(
        testUser.id,
        'Password123!',
        'NewPassword123!'
      );
      expect(result).toBe(true);

      // Vérifier que le nouveau mot de passe fonctionne
      const loginResult = await AuthService.login('<EMAIL>', 'NewPassword123!');
      expect(loginResult.token).toBeDefined();
    });

    it('should not change password with invalid old password', async () => {
      await expect(AuthService.changePassword(
        testUser.id,
        'WrongPassword123!',
        'NewPassword123!'
      )).rejects.toThrow('Ancien mot de passe incorrect');
    });
  });

  describe('resetPassword', () => {
    let testUser;

    beforeAll(async () => {
      testUser = await AuthService.createUser({
        username: 'resettest',
        email: '<EMAIL>',
        password: 'Reset123!',
        first_name: 'Reset',
        last_name: 'Test'
      }, 'admin');
    });

    it('should generate reset token for valid email', async () => {
      const result = await AuthService.resetPassword('<EMAIL>');
      expect(result).toBe(true);
    });

    it('should not generate reset token for invalid email', async () => {
      await expect(AuthService.resetPassword('<EMAIL>'))
        .rejects
        .toThrow('Utilisateur non trouvé');
    });
  });
});