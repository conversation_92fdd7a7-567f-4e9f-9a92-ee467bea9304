const express = require('express');
const router = express.Router();
const { auth, checkRole } = require('../middleware/auth');
const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const weatherService = require('../services/weatherService');
const analyticsService = require('../services/analyticsService');

// Load IoT service conditionally
let iotService = null;
try {
  iotService = require('../services/iotService');
  console.log('✅ IoT service loaded successfully');
} catch (error) {
  console.log('⚠️ IoT service failed to load:', error.message);
  // Create a mock IoT service for fallback
  iotService = {
    getDevices: () => [],
    getSensorData: () => [],
    getAlerts: () => [],
    sendCommand: () => 'mock-request-id',
    acknowledgeAlert: () => {}
  };
}

// @route   GET /api/integrations/iot/devices
// @desc    Get IoT devices for current user
// @access  Private
router.get('/iot/devices', auth, async (req, res) => {
  try {
    const devices = iotService.getDevices();

    // Enrichir les données avec les dernières mesures
    const enrichedDevices = devices.map(device => {
      const latestData = iotService.getSensorData(device.id, 1);
      return {
        ...device,
        last_reading: latestData.length > 0 ? latestData[0] : null
      };
    });

    res.json({
      status: 'success',
      data: enrichedDevices,
      count: enrichedDevices.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des dispositifs IoT:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des dispositifs IoT',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/iot/alerts
// @desc    Get IoT alerts for current user
// @access  Private
router.get('/iot/alerts', auth, async (req, res) => {
  try {
    const { acknowledged } = req.query;
    const acknowledgedFilter = acknowledged !== undefined ? acknowledged === 'true' : null;

    const alerts = iotService.getAlerts(acknowledgedFilter);

    res.json({
      status: 'success',
      data: alerts,
      count: alerts.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des alertes IoT:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des alertes IoT',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/iot/stats
// @desc    Get IoT statistics for current user
// @access  Private
router.get('/iot/stats', auth, async (req, res) => {
  try {
    // Mock IoT statistics
    const stats = {
      total_devices: 3,
      active_devices: 3,
      offline_devices: 0,
      total_alerts: 2,
      unacknowledged_alerts: 1,
      average_temperature: 22.5,
      average_humidity: 65.2,
      feed_level: 75,
      last_update: new Date().toISOString()
    };

    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques IoT:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des statistiques IoT'
    });
  }
});

// @route   POST /api/integrations/iot/command
// @desc    Send command to IoT device
// @access  Private
router.post('/iot/command', auth, async (req, res) => {
  try {
    const { device_id, command, parameters } = req.body;

    if (!device_id || !command) {
      return res.status(400).json({
        status: 'error',
        message: 'Device ID et commande requis'
      });
    }

    // Envoyer la commande via le service IoT
    const requestId = iotService.sendCommand(device_id, command, parameters);

    const result = {
      device_id,
      command,
      parameters,
      request_id: requestId,
      status: 'sent',
      timestamp: new Date().toISOString()
    };

    res.json({
      status: 'success',
      data: result,
      message: 'Commande envoyée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la commande IoT:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de l\'envoi de la commande IoT',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/iot/sensor/:sensorId/data
// @desc    Get sensor data history
// @access  Private
router.get('/iot/sensor/:sensorId/data', auth, async (req, res) => {
  try {
    const { sensorId } = req.params;
    const { limit = 50 } = req.query;

    const sensorData = iotService.getSensorData(sensorId, parseInt(limit));

    res.json({
      status: 'success',
      data: sensorData,
      sensor_id: sensorId,
      count: sensorData.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des données du capteur:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des données du capteur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/integrations/iot/alerts/:alertId/acknowledge
// @desc    Acknowledge an IoT alert
// @access  Private
router.post('/iot/alerts/:alertId/acknowledge', auth, async (req, res) => {
  try {
    const { alertId } = req.params;

    iotService.acknowledgeAlert(alertId);

    res.json({
      status: 'success',
      message: 'Alerte acquittée avec succès',
      alert_id: alertId
    });
  } catch (error) {
    console.error('Erreur lors de l\'acquittement de l\'alerte:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de l\'acquittement de l\'alerte',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/weather/current
// @desc    Get current weather data
// @access  Private
router.get('/weather/current', auth, async (req, res) => {
  try {
    const { lat, lon, location } = req.query;

    // Préparer les paramètres de localisation
    let locationParams = null;
    if (lat && lon) {
      locationParams = { lat: parseFloat(lat), lon: parseFloat(lon) };
    } else if (location) {
      locationParams = { name: location };
    }

    // Récupérer les données météo via le service
    const weather = await weatherService.getCurrentWeather(locationParams);

    res.json({
      status: 'success',
      data: weather
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des données météo:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des données météo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/weather/forecast
// @desc    Get weather forecast
// @access  Private
router.get('/weather/forecast', auth, async (req, res) => {
  try {
    const { days = 5, lat, lon, location } = req.query;

    // Préparer les paramètres de localisation
    let locationParams = null;
    if (lat && lon) {
      locationParams = { lat: parseFloat(lat), lon: parseFloat(lon) };
    } else if (location) {
      locationParams = { name: location };
    }

    // Récupérer les prévisions via le service
    const forecast = await weatherService.getForecast(locationParams, parseInt(days));

    res.json({
      status: 'success',
      data: forecast,
      days: parseInt(days)
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des prévisions météo:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des prévisions météo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/weather/alerts
// @desc    Get weather alerts for agricultural activities
// @access  Private
router.get('/weather/alerts', auth, async (req, res) => {
  try {
    const { lat, lon, location } = req.query;

    // Préparer les paramètres de localisation
    let locationParams = null;
    if (lat && lon) {
      locationParams = { lat: parseFloat(lat), lon: parseFloat(lon) };
    } else if (location) {
      locationParams = { name: location };
    }

    // Récupérer les alertes météo
    const alerts = await weatherService.getWeatherAlerts(locationParams);

    res.json({
      status: 'success',
      data: alerts,
      count: alerts.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des alertes météo:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des alertes météo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/weather/recommendations
// @desc    Get weather-based recommendations for poultry farming
// @access  Private
router.get('/weather/recommendations', auth, async (req, res) => {
  try {
    const { lat, lon, location, type = 'general' } = req.query;

    // Préparer les paramètres de localisation
    let locationParams = null;
    if (lat && lon) {
      locationParams = { lat: parseFloat(lat), lon: parseFloat(lon) };
    } else if (location) {
      locationParams = { name: location };
    }

    // Récupérer les recommandations météo via le service
    const recommendations = await weatherService.getRecommendations(locationParams, type);

    res.json({
      status: 'success',
      data: recommendations,
      type: type
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des recommandations météo:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des recommandations météo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/analytics/comprehensive
// @desc    Get comprehensive analytics dashboard
// @access  Private
router.get('/analytics/comprehensive', auth, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    const eleveurId = req.user.profile_id || req.user.id;

    if (!eleveurId) {
      return res.status(400).json({
        status: 'error',
        message: 'ID éleveur requis pour les analytics'
      });
    }

    const analytics = await analyticsService.getComprehensiveAnalytics(eleveurId, period);

    res.json({
      status: 'success',
      data: analytics
    });
  } catch (error) {
    console.error('Erreur lors de la génération des analytics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la génération des analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/analytics/production
// @desc    Get production metrics only
// @access  Private
router.get('/analytics/production', auth, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    const eleveurId = req.user.profile_id || req.user.id;

    if (!eleveurId) {
      return res.status(400).json({
        status: 'error',
        message: 'ID éleveur requis pour les analytics'
      });
    }

    const productionMetrics = await analyticsService.getProductionMetrics(eleveurId, period);

    res.json({
      status: 'success',
      data: productionMetrics,
      period
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des métriques de production:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des métriques de production',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/analytics/financial
// @desc    Get financial metrics only
// @access  Private
router.get('/analytics/financial', auth, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    const eleveurId = req.user.profile_id || req.user.id;

    if (!eleveurId) {
      return res.status(400).json({
        status: 'error',
        message: 'ID éleveur requis pour les analytics'
      });
    }

    const financialMetrics = await analyticsService.getFinancialMetrics(eleveurId, period);

    res.json({
      status: 'success',
      data: financialMetrics,
      period
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des métriques financières:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des métriques financières',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/analytics/kpis
// @desc    Get performance KPIs
// @access  Private
router.get('/analytics/kpis', auth, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    const eleveurId = req.user.profile_id || req.user.id;

    if (!eleveurId) {
      return res.status(400).json({
        status: 'error',
        message: 'ID éleveur requis pour les analytics'
      });
    }

    const kpis = await analyticsService.getPerformanceKPIs(eleveurId, period);

    res.json({
      status: 'success',
      data: kpis,
      period
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des KPIs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des KPIs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/integrations/analytics/predictions
// @desc    Get predictive analytics
// @access  Private
router.get('/analytics/predictions', auth, async (req, res) => {
  try {
    const eleveurId = req.user.profile_id || req.user.id;

    if (!eleveurId) {
      return res.status(400).json({
        status: 'error',
        message: 'ID éleveur requis pour les analytics'
      });
    }

    const predictions = await analyticsService.getPredictiveAnalytics(eleveurId);

    res.json({
      status: 'success',
      data: predictions
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des prédictions:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des prédictions',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
