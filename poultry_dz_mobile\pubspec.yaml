name: poultry_dz_mobile
description: Application mobile pour la gestion d'élevage de volailles.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6
  flutter_riverpod: ^2.4.9
  http: ^1.1.2
  shared_preferences: ^2.2.2
  local_auth: ^2.1.7
  local_auth_android: ^1.0.35
  local_auth_ios: ^1.1.5
  intl: ^0.18.1
  path: ^1.8.3
  path_provider: ^2.1.1
  image_picker: ^1.0.5
  file_picker: ^6.1.1
  url_launcher: ^6.2.2
  package_info_plus: ^5.0.1
  device_info_plus: ^9.1.1
  connectivity_plus: ^5.0.2
  flutter_secure_storage: ^9.0.0
  permission_handler: ^11.1.0
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  mockito: ^5.4.3
  flutter_gen_runner: ^5.3.2

flutter:
  uses-material-design: true
  assets:
    - assets/logos/
    - assets/images/
    - assets/icons/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700

flutter_icons:
  android: true
  ios: true
  image_path: "assets/logos/logo.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/logos/logo_foreground.png"

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/logos/logo.png
  branding: assets/logos/branding.png
  color_dark: "#121212"
  image_dark: assets/logos/logo_dark.png
  branding_dark: assets/logos/branding_dark.png

  android_12:
    image: assets/logos/logo.png
    icon_background_color: "#FFFFFF"
    image_dark: assets/logos/logo_dark.png
    icon_background_color_dark: "#121212"