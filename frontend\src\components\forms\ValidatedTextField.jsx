import React, { useState } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Info,
  CheckCircle,
  Error
} from '@mui/icons-material';

/**
 * Composant TextField avec validation intégrée
 */
const ValidatedTextField = ({
  name,
  label,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  helperText,
  required = false,
  disabled = false,
  multiline = false,
  rows = 1,
  placeholder,
  autoComplete,
  autoFocus = false,
  fullWidth = true,
  margin = 'normal',
  variant = 'outlined',
  size = 'medium',
  showValidationIcon = true,
  tooltip,
  maxLength,
  title, // Extract title to prevent it from being passed to TextField
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [focused, setFocused] = useState(false);

  const isPassword = type === 'password';
  const hasValue = value && value.toString().length > 0;
  const isValid = hasValue && !error;

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  const handleFocus = (event) => {
    setFocused(true);
    if (props.onFocus) {
      props.onFocus(event);
    }
  };

  const handleBlur = (event) => {
    setFocused(false);
    if (onBlur) {
      onBlur(event);
    }
  };

  const getInputProps = () => {
    const inputProps = {};

    // Limitation de caractères
    if (maxLength) {
      inputProps.maxLength = maxLength;
    }

    return inputProps;
  };

  const getEndAdornment = () => {
    const adornments = [];

    // Icône de validation
    if (showValidationIcon && hasValue && !focused) {
      if (isValid) {
        adornments.push(
          <Tooltip key="valid" title="Valide">
            <CheckCircle color="success" fontSize="small" />
          </Tooltip>
        );
      } else if (error) {
        adornments.push(
          <Tooltip key="error" title={error}>
            <Error color="error" fontSize="small" />
          </Tooltip>
        );
      }
    }

    // Bouton pour afficher/masquer le mot de passe
    if (isPassword) {
      adornments.push(
        <IconButton
          key="password-toggle"
          aria-label="toggle password visibility"
          onClick={handleTogglePassword}
          edge="end"
          size="small"
        >
          {showPassword ? <VisibilityOff /> : <Visibility />}
        </IconButton>
      );
    }

    // Icône d'information
    if (tooltip) {
      adornments.push(
        <Tooltip key="info" title={tooltip}>
          <IconButton size="small" edge="end">
            <Info fontSize="small" />
          </IconButton>
        </Tooltip>
      );
    }

    return adornments.length > 0 ? (
      <InputAdornment position="end">
        {adornments}
      </InputAdornment>
    ) : null;
  };

  const getHelperText = () => {
    if (error) {
      return error.toString();
    }

    if (helperText) {
      return helperText.toString();
    }

    if (maxLength && focused) {
      const currentLength = value ? value.toString().length : 0;
      return `${currentLength}/${maxLength} caractères`;
    }

    return '';
  };

  return (
    <TextField
      name={name}
      label={label}
      type={isPassword ? (showPassword ? 'text' : 'password') : type}
      value={value || ''}
      onChange={onChange}
      onBlur={handleBlur}
      onFocus={handleFocus}
      error={!!error}
      helperText={getHelperText()}
      required={required}
      disabled={disabled}
      multiline={multiline}
      rows={multiline ? rows : undefined}
      placeholder={placeholder}
      autoComplete={autoComplete}
      autoFocus={autoFocus}
      fullWidth={fullWidth}
      margin={margin}
      variant={variant}
      size={size}
      inputProps={getInputProps()}
      InputProps={{
        endAdornment: getEndAdornment(),
        ...props.InputProps
      }}
      {...props}
    />
  );
};

export default ValidatedTextField;
