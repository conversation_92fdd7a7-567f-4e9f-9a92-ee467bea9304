/**
 * Service API pour les fonctionnalités éleveur
 */

import axiosInstance from '../utils/axiosConfig';

const eleveurAPI = {
  // === GESTION DES VENTES ===

  // Récupérer toutes les ventes de l'éleveur
  getVentes: (eleveurId) => {
    return axiosInstance.get(`/ventes/eleveur/${eleveurId}`);
  },

  // Créer une nouvelle vente
  createVente: (venteData) => {
    return axiosInstance.post('/ventes', venteData);
  },

  // Mettre à jour une vente
  updateVente: (venteId, venteData) => {
    return axiosInstance.put(`/ventes/${venteId}`, venteData);
  },

  // Supprimer une vente
  deleteVente: (venteId) => {
    return axiosInstance.delete(`/ventes/${venteId}`);
  },

  // Récupérer les statistiques des ventes
  getVentesStats: (eleveurId) => {
    return axiosInstance.get(`/ventes/eleveur/${eleveurId}/statistiques`);
  },

  // === STATISTIQUES DÉTAILLÉES ===

  // Récupérer les statistiques détaillées
  getDetailedStatistics: (eleveurId, period = '6months') => {
    return axiosInstance.get(`/eleveurs/${eleveurId}/dashboard?period=${period}`);
  },

  // Récupérer les KPIs
  getKPIs: (eleveurId, period = '6months') => {
    return axiosInstance.get(`/eleveurs/${eleveurId}/dashboard?period=${period}`);
  },

  // Récupérer les données de production
  getProductionData: (period = '6months') => {
    return axiosInstance.get(`/eleveur/statistics/production?period=${period}`);
  },

  // Récupérer les données de santé
  getHealthData: (period = '6months') => {
    return axiosInstance.get(`/eleveur/statistics/health?period=${period}`);
  },

  // Récupérer les données financières
  getFinancialData: (period = '6months') => {
    return axiosInstance.get(`/eleveur/statistics/financial?period=${period}`);
  },

  // === GESTION DES FERMES ===

  // Récupérer les fermes de l'éleveur
  getFermes: () => {
    return axiosInstance.get('/eleveur/fermes');
  },

  // Récupérer une ferme spécifique
  getFerme: (fermeId) => {
    return axiosInstance.get(`/eleveur/fermes/${fermeId}`);
  },

  // Mettre à jour les informations d'une ferme
  updateFerme: (fermeId, fermeData) => {
    return axiosInstance.put(`/eleveur/fermes/${fermeId}`, fermeData);
  },

  // === GESTION DES VOLAILLES ===

  // Récupérer toutes les volailles
  getVolailles: () => {
    return axiosInstance.get('/eleveur/volailles');
  },

  // Récupérer les volailles par ferme
  getVolaillesByFerme: (fermeId) => {
    return axiosInstance.get(`/eleveur/volailles?ferme_id=${fermeId}`);
  },

  // Créer un nouveau lot de volailles
  createVolaille: (volailleData) => {
    return axiosInstance.post('/eleveur/volailles', volailleData);
  },

  // Mettre à jour un lot de volailles
  updateVolaille: (volailleId, volailleData) => {
    return axiosInstance.put(`/eleveur/volailles/${volailleId}`, volailleData);
  },

  // Supprimer un lot de volailles
  deleteVolaille: (volailleId) => {
    return axiosInstance.delete(`/eleveur/volailles/${volailleId}`);
  },

  // === PRODUCTION ===

  // Enregistrer la production quotidienne
  recordProduction: (productionData) => {
    return axiosInstance.post('/eleveur/production', productionData);
  },

  // Récupérer l'historique de production
  getProductionHistory: (period = '1month') => {
    return axiosInstance.get(`/eleveur/production/history?period=${period}`);
  },

  // === SANTÉ ET VÉTÉRINAIRE ===

  // Enregistrer un événement de santé
  recordHealthEvent: (healthData) => {
    return axiosInstance.post('/eleveur/health', healthData);
  },

  // Récupérer l'historique de santé
  getHealthHistory: () => {
    return axiosInstance.get('/eleveur/health/history');
  },

  // Programmer une consultation vétérinaire
  scheduleConsultation: (consultationData) => {
    return axiosInstance.post('/eleveur/consultations', consultationData);
  },

  // === ALIMENTATION ===

  // Enregistrer la consommation d'aliments
  recordFeeding: (feedingData) => {
    return axiosInstance.post('/eleveur/feeding', feedingData);
  },

  // Récupérer l'historique d'alimentation
  getFeedingHistory: () => {
    return axiosInstance.get('/eleveur/feeding/history');
  },

  // === ALERTES ===

  // Récupérer les alertes actives
  getAlerts: () => {
    return axiosInstance.get('/eleveur/alerts');
  },

  // Marquer une alerte comme lue
  markAlertAsRead: (alertId) => {
    return axiosInstance.put(`/eleveur/alerts/${alertId}/read`);
  },

  // === RAPPORTS ===

  // Générer un rapport personnalisé
  generateReport: (reportConfig) => {
    return axiosInstance.post('/eleveur/reports/generate', reportConfig);
  },

  // Récupérer les rapports disponibles
  getAvailableReports: () => {
    return axiosInstance.get('/eleveur/reports');
  },

  // Télécharger un rapport
  downloadReport: (reportId) => {
    return axiosInstance.get(`/eleveur/reports/${reportId}/download`, {
      responseType: 'blob'
    });
  },

  // === NOTIFICATIONS ===

  // Récupérer les notifications
  getNotifications: () => {
    return axiosInstance.get('/eleveur/notifications');
  },

  // Marquer une notification comme lue
  markNotificationAsRead: (notificationId) => {
    return axiosInstance.put(`/eleveur/notifications/${notificationId}/read`);
  },

  // === PARAMÈTRES ===

  // Récupérer les paramètres de l'éleveur
  getSettings: () => {
    return axiosInstance.get('/eleveur/settings');
  },

  // Mettre à jour les paramètres
  updateSettings: (settings) => {
    return axiosInstance.put('/eleveur/settings', settings);
  },

  // === DASHBOARD ===

  // Récupérer les données du dashboard
  getDashboardData: () => {
    return axiosInstance.get('/eleveur/dashboard');
  },

  // Récupérer les métriques en temps réel
  getRealTimeMetrics: () => {
    return axiosInstance.get('/eleveur/metrics/realtime');
  }
};

export { eleveurAPI };
export default eleveurAPI;
