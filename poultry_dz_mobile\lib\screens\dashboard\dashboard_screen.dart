import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_config.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  final User user;

  const DashboardScreen({required this.user, super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final roleConfig = AppConfig.getRoleConfig(widget.user.role);
    final features = roleConfig['features'] as List<String>;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Tableau de bord - ${widget.user.role.toUpperCase()}',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.person_outline),
            onPressed: () {
              // TODO: Implement profile
            },
          ),
        ],
      ),
      body: SafeArea(
        child: _buildDashboardContent(features),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(features),
    );
  }

  Widget _buildDashboardContent(List<String> features) {
    switch (_currentIndex) {
      case 0:
        return _buildHomeTab(features);
      case 1:
        return _buildFeaturesTab(features);
      case 2:
        return _buildReportsTab();
      case 3:
        return _buildSettingsTab();
      default:
        return const Center(child: Text('Page non trouvée'));
    }
  }

  Widget _buildHomeTab(List<String> features) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 24),
          _buildQuickActions(features),
          const SizedBox(height: 24),
          _buildRecentActivities(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bienvenue, ${widget.user.name}',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Que souhaitez-vous faire aujourd\'hui ?',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(List<String> features) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions rapides',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
          ),
          itemCount: features.length,
          itemBuilder: (context, index) {
            final feature = features[index];
            return _buildFeatureCard(feature);
          },
        ),
      ],
    );
  }

  Widget _buildFeatureCard(String feature) {
    IconData icon;
    String title;

    switch (feature) {
      case 'livestock':
        icon = Icons.pets;
        title = 'Élevage';
        break;
      case 'health':
        icon = Icons.local_hospital;
        title = 'Santé';
        break;
      case 'feeding':
        icon = Icons.restaurant;
        title = 'Alimentation';
        break;
      case 'reports':
        icon = Icons.bar_chart;
        title = 'Rapports';
        break;
      case 'users':
        icon = Icons.people;
        title = 'Utilisateurs';
        break;
      case 'settings':
        icon = Icons.settings;
        title = 'Paramètres';
        break;
      default:
        icon = Icons.circle;
        title = feature;
    }

    return Card(
      child: InkWell(
        onTap: () {
          // TODO: Navigate to feature
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Activités récentes',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              return ListTile(
                leading: const CircleAvatar(
                  child: Icon(Icons.history),
                ),
                title: Text('Activité ${index + 1}'),
                subtitle: Text('Il y a ${index + 1} heures'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  // TODO: Navigate to activity details
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturesTab(List<String> features) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: features.length,
      itemBuilder: (context, index) {
        final feature = features[index];
        return _buildFeatureCard(feature);
      },
    );
  }

  Widget _buildReportsTab() {
    return const Center(
      child: Text('Rapports à venir'),
    );
  }

  Widget _buildSettingsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        ListTile(
          leading: const Icon(Icons.person_outline),
          title: const Text('Profil'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Navigate to profile
          },
        ),
        ListTile(
          leading: const Icon(Icons.notifications_outlined),
          title: const Text('Notifications'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Navigate to notifications
          },
        ),
        ListTile(
          leading: const Icon(Icons.language_outlined),
          title: const Text('Langue'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Navigate to language settings
          },
        ),
        ListTile(
          leading: const Icon(Icons.security_outlined),
          title: const Text('Sécurité'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Navigate to security settings
          },
        ),
        ListTile(
          leading: const Icon(Icons.help_outline),
          title: const Text('Aide'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Navigate to help
          },
        ),
        ListTile(
          leading: const Icon(Icons.logout, color: Colors.red),
          title: const Text(
            'Déconnexion',
            style: TextStyle(color: Colors.red),
          ),
          onTap: () {
            ref.read(authProvider.notifier).logout();
          },
        ),
      ],
    );
  }

  Widget _buildBottomNavigationBar(List<String> features) {
    return NavigationBar(
      selectedIndex: _currentIndex,
      onDestinationSelected: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      destinations: const [
        NavigationDestination(
          icon: Icon(Icons.home_outlined),
          selectedIcon: Icon(Icons.home),
          label: 'Accueil',
        ),
        NavigationDestination(
          icon: Icon(Icons.apps_outlined),
          selectedIcon: Icon(Icons.apps),
          label: 'Fonctionnalités',
        ),
        NavigationDestination(
          icon: Icon(Icons.bar_chart_outlined),
          selectedIcon: Icon(Icons.bar_chart),
          label: 'Rapports',
        ),
        NavigationDestination(
          icon: Icon(Icons.settings_outlined),
          selectedIcon: Icon(Icons.settings),
          label: 'Paramètres',
        ),
      ],
    );
  }
}