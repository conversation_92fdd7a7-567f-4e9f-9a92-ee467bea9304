const { Ouv<PERSON>, Eleveur, User, Role } = require('../models');
const OuvrierService = require('../services/ouvrierService');

describe('OuvrierService Tests', () => {
  let eleveur, ouvrier, ouvrierUser;
  let adminRole, ouvrierRole;

  beforeAll(async () => {
    // Créer les rôles
    adminRole = await Role.create({
      name: 'admin',
      description: 'Admin role',
      permissions: ['manage_users', 'manage_ouvriers']
    });

    ouvrierRole = await Role.create({
      name: 'ouvrier',
      description: 'Ouvrier role',
      permissions: ['view_own_profile']
    });

    // Créer un éleveur
    eleveur = await Eleveur.create({
      nom: 'Test',
      prenom: 'Eleveur',
      email: '<EMAIL>',
      telephone: '0123456789',
      adresse: 'Test address'
    });

    // Créer un ouvrier avec compte utilisateur
    ouvrier = await <PERSON><PERSON>v<PERSON>.create({
      nom: 'Test',
      prenom: 'Ouvrier',
      email: '<EMAIL>',
      telephone: '0123456789',
      adresse: 'Test address',
      date_embauche: new Date(),
      salaire: 30000,
      eleveur_id: eleveur.id
    });

    ouvrierUser = await User.create({
      username: 'testouvrier',
      email: '<EMAIL>',
      password: 'Ouvrier123!',
      role_id: ouvrierRole.id,
      profile_id: ouvrier.id,
      status: 'active'
    });

    // Mettre à jour l'ouvrier avec l'ID utilisateur
    await ouvrier.update({ user_id: ouvrierUser.id });
  });

  afterAll(async () => {
    // Nettoyer la base de données
    await User.destroy({ where: {} });
    await Role.destroy({ where: {} });
    await Eleveur.destroy({ where: {} });
    await Ouvrier.destroy({ where: {} });
  });

  describe('getAllOuvriers', () => {
    it('should get all ouvriers with filters', async () => {
      const filters = {
        nom: 'Test',
        statut: 'actif'
      };

      const ouvriers = await OuvrierService.getAllOuvriers(filters);
      expect(Array.isArray(ouvriers)).toBe(true);
      expect(ouvriers.length).toBeGreaterThan(0);
      expect(ouvriers[0].nom).toBe('Test');
    });

    it('should get ouvriers by eleveur', async () => {
      const ouvriers = await OuvrierService.getOuvriersByEleveur(eleveur.id);
      expect(Array.isArray(ouvriers)).toBe(true);
      expect(ouvriers.length).toBeGreaterThan(0);
      expect(ouvriers[0].eleveur_id).toBe(eleveur.id);
    });
  });

  describe('getOuvrierById', () => {
    it('should get ouvrier by id', async () => {
      const foundOuvrier = await OuvrierService.getOuvrierById(ouvrier.id);
      expect(foundOuvrier).toBeDefined();
      expect(foundOuvrier.id).toBe(ouvrier.id);
      expect(foundOuvrier.user_account).toBeDefined();
      expect(foundOuvrier.eleveur).toBeDefined();
    });

    it('should return null for non-existent ouvrier', async () => {
      const foundOuvrier = await OuvrierService.getOuvrierById(999999);
      expect(foundOuvrier).toBeNull();
    });
  });

  describe('updateOuvrier', () => {
    it('should update ouvrier details', async () => {
      const updates = {
        telephone: '**********',
        adresse: 'Updated address',
        salaire: 35000
      };

      const updatedOuvrier = await OuvrierService.updateOuvrier(ouvrier.id, updates);
      expect(updatedOuvrier).toBeDefined();
      expect(updatedOuvrier.telephone).toBe(updates.telephone);
      expect(updatedOuvrier.adresse).toBe(updates.adresse);
      expect(updatedOuvrier.salaire).toBe(updates.salaire);
    });

    it('should throw error for non-existent ouvrier', async () => {
      const updates = {
        telephone: '**********'
      };

      await expect(OuvrierService.updateOuvrier(999999, updates))
        .rejects
        .toThrow('Ouvrier non trouvé');
    });
  });

  describe('updateOuvrierStatus', () => {
    it('should update ouvrier status', async () => {
      const updatedOuvrier = await OuvrierService.updateOuvrierStatus(ouvrier.id, 'inactif');
      expect(updatedOuvrier).toBeDefined();
      expect(updatedOuvrier.statut).toBe('inactif');

      // Vérifier que le compte utilisateur est également désactivé
      const user = await User.findByPk(ouvrierUser.id);
      expect(user.status).toBe('inactive');
    });

    it('should throw error for invalid status', async () => {
      await expect(OuvrierService.updateOuvrierStatus(ouvrier.id, 'invalid_status'))
        .rejects
        .toThrow('Statut invalide');
    });
  });

  describe('deleteOuvrier', () => {
    it('should delete ouvrier and associated user account', async () => {
      await OuvrierService.deleteOuvrier(ouvrier.id);

      // Vérifier que l'ouvrier est supprimé
      const deletedOuvrier = await Ouvrier.findByPk(ouvrier.id);
      expect(deletedOuvrier).toBeNull();

      // Vérifier que le compte utilisateur est supprimé
      const deletedUser = await User.findByPk(ouvrierUser.id);
      expect(deletedUser).toBeNull();
    });

    it('should throw error for non-existent ouvrier', async () => {
      await expect(OuvrierService.deleteOuvrier(999999))
        .rejects
        .toThrow('Ouvrier non trouvé');
    });
  });
});