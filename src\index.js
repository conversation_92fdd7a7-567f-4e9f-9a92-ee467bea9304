require('dotenv').config();
const express = require('express');
const cors = require('cors');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const path = require('path');
console.log('Environment variables loaded:', {
  PORT: process.env.PORT,
  NODE_ENV: process.env.NODE_ENV,
  DB_HOST: process.env.DB_HOST
});
const sequelize = require('./config/database');

// Load Swagger documentation
const swaggerDocument = YAML.load(path.join(__dirname, '../docs/api.yaml'));

// Initialiser tous les modèles via le système d'index
const db = require('./models');
console.log('Modèles chargés depuis db:', Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize'));

// Import des middlewares
const tokenRefresh = require('./middleware/tokenRefresh');

// Import des routes
// Import des routes
const eleveurRoutes = require('./routes/eleveurRoutes');
const volailleRoutes = require('./routes/volailleRoutes');
const authRoutes = require('./routes/authRoutes');
const aiRoutes = require('./routes/aiRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const adminRoutes = require('./routes/adminRoutes');
const marchandRoutes = require('./routes/marchandRoutes');
const blogRoutes = require('./routes/blogRoutes');
const veterinaireRoutes = require('./routes/veterinaireRoutes');
const translationRoutes = require('./routes/translationRoutes');
const iaAnalysisRoutes = require('./routes/iaAnalysisRoutes');
const disponibiliteRoutes = require('./routes/disponibiliteRoutes');
const venteRoutes = require('./routes/venteRoutes');
const marketplaceRoutes = require('./routes/marketplaceRoutes');
const homepageSectionRoutes = require('./routes/homepageSectionRoutes');
const ouvrierRoutes = require('./routes/ouvrierRoutes');
// Nouvelles routes pour l'amélioration du tableau de bord éleveur - Phase 1
const poussinRoutes = require('./routes/poussinRoutes');
const productionOeufsRoutes = require('./routes/productionOeufsRoutes');
const suiviVeterinaireRoutes = require('./routes/suiviVeterinaireRoutes');
const alerteStockRoutes = require('./routes/alerteStockRoutes');
// Nouvelles routes générées
const prescriptionRoutes = require('./routes/prescriptionRoutes');
const produitRoutes = require('./routes/produitRoutes');
const feedRoutes = require('./routes/feed');

// Import des services
const { createNotificationsTable } = require('./services/notifications');
const seedApiKeys = require('./scripts/seedApiKeys'); // Import du script de seed API keys
const ensureApiConfigTable = require('./scripts/ensureApiConfigTable'); // Import du script pour vérifier la table ApiConfig

const app = express();

// Middleware
app.use(cors({
  origin: true, // Allow all origins in development
  credentials: true,
  exposedHeaders: ['x-auth-token-refreshed'], // Exposer l'en-tête pour le rafraîchissement du token
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Appliquer le middleware de rafraîchissement des tokens
app.use(tokenRefresh);

// Middleware de journalisation des requêtes
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Fonction pour ajouter les colonnes manquantes avec des valeurs par défaut
async function addMissingColumns() {
  try {
    // Ajouter date_modification à la table eleveurs si elle n'existe pas
    await sequelize.query(`
      ALTER TABLE eleveurs
      ADD COLUMN IF NOT EXISTS date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);

    // Ajouter date_modification à la table volailles si elle n'existe pas
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);

    // Ajouter lot_numero à la table volailles si elle n'existe pas
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS lot_numero VARCHAR(50)
    `);

    // Create ENUM type if it doesn't exist
    await sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_volailles_type_elevage') THEN
          CREATE TYPE enum_volailles_type_elevage AS ENUM ('chair', 'pondeuse', 'reproducteur', 'mixte');
        END IF;
      END $$;
    `);

    // Add type_elevage column
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS type_elevage enum_volailles_type_elevage DEFAULT 'chair'
    `);

    // Create ENUM type for statut if it doesn't exist
    await sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_volailles_statut') THEN
          CREATE TYPE enum_volailles_statut AS ENUM ('actif', 'vendu', 'abattu', 'transfere', 'archive');
        END IF;
      END $$;
    `);

    // Add statut column
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS statut enum_volailles_statut DEFAULT 'actif'
    `);

    // Add date_acquisition column
    await sequelize.query(`
      ALTER TABLE volailles
      ADD COLUMN IF NOT EXISTS date_acquisition TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);

    console.log('Colonnes manquantes ajoutées avec succès');
  } catch (error) {
    console.log('Certaines colonnes existent déjà ou erreur mineure:', error.message);
  }
}

// Fonction asynchrone pour initialiser la base de données
async function initDatabase() {
  try {
    // D'abord ajouter les colonnes manquantes avec des valeurs par défaut
    await addMissingColumns();

    // Désactiver temporairement la synchronisation automatique
    // await sequelize.sync({
    //   force: false,
    //   alter: true,
    //   logging: console.log
    // });
    console.log('Base de données synchronisée avec succès');

    // Créer la table de notifications
    await createNotificationsTable();

    // Vérifier et mettre à jour la structure de la table ApiConfig
    if (typeof ensureApiConfigTable === 'function') {
      await ensureApiConfigTable();
      console.log('ApiConfig table structure verified');
    } else {
      console.error('ensureApiConfigTable is not a function, skipping table verification');
    }

    // Initialiser les clés API par défaut
    await seedApiKeys();
    console.log('API keys initialized successfully');
  } catch (err) {
    console.error('Erreur lors de la synchronisation:', err);
    process.exit(1);
  }
}

// Initialiser la base de données
initDatabase();

// Routes de base
app.get('/', (req, res) => {
  res.json({ message: 'Bienvenue sur l\'API Poultray DZ' });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Routes API
app.use('/api/eleveurs', eleveurRoutes);
app.use('/api/volailles', volailleRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/marchands', marchandRoutes);
app.use('/api/blog', blogRoutes);
app.use('/api/veterinaires', veterinaireRoutes);
app.use('/api/translations', translationRoutes);
app.use('/api/ia-analysis', iaAnalysisRoutes);
app.use('/api/disponibilites', disponibiliteRoutes);
app.use('/api/ventes', venteRoutes);
app.use('/api/marketplace', marketplaceRoutes);
app.use('/api/homepage-sections', homepageSectionRoutes);
app.use('/api/poussins', poussinRoutes);
app.use('/api/production-oeufs', productionOeufsRoutes);
app.use('/api/suivi-veterinaire', suiviVeterinaireRoutes);
app.use('/api/alerte-stock', alerteStockRoutes);
app.use('/api/prescriptions', prescriptionRoutes);
app.use('/api/produits', produitRoutes);
app.use('/api/feed', feedRoutes);
app.use('/api/ouvriers', ouvrierRoutes);

// Swagger UI Documentation
const swaggerOptions = {
  explorer: true,
  customCss: '.swagger-ui .topbar { background-color: #2c5530; }',
  customSiteTitle: 'Poultry DZ API Documentation',
  swaggerOptions: {
    validatorUrl: null
  }
};
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, swaggerOptions));

// Port d'écoute - utilise le port 3003 depuis .env
const PORT = process.env.PORT || 3003;
app.listen(PORT, () => {
  console.log(`Serveur démarré sur le port ${PORT}`);
});
