/**
 * Enhanced Veterinaire Dashboard Routes
 * Handles all veterinaire-related functionality including:
 * - Dashboard statistics
 * - Consultations management
 * - Prescriptions
 * - Notifications and alerts
 */

const express = require('express');
const router = express.Router();
const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const { auth, checkRole } = require('../middleware/auth');
const { getProfile, updateProfile, updateDisponibilites } = require('../controllers/veterinaireController');
const models = require('../models');

// Middleware pour vérifier si l'utilisateur est un vétérinaire
const isVeterinaire = async (req, res, next) => {
  try {
    if (req.user.role !== 'veterinaire') {
      return res.status(403).json({ message: 'Accès refusé. Rôle vétérinaire requis.' });
    }
    next();
  } catch (error) {
    console.error('Erreur dans le middleware isVeterinaire:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
};

// Routes pour la gestion du profil vétérinaire
router.get('/profile', auth, isVeterinaire, getProfile);
router.put('/profile', auth, isVeterinaire, updateProfile);
router.put('/disponibilites', auth, isVeterinaire, updateDisponibilites);

// @route   GET /api/veterinaire/dashboard
// @desc    Get enhanced veterinaire dashboard data
// @access  Private/Veterinaire
router.get('/dashboard', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    // Statistiques simplifiées pour éviter les erreurs
    let stats = {
      total_prescriptions: 0,
      prescriptions_mois: 0,
      total_consultations: 0,
      consultations_mois: 0,
      eleveurs_suivis: 0,
      consultations_semaine_prochaine: 0,
      prescriptions_actives: 0,
      satisfaction_moyenne: 4.2,
      cout_moyen_consultation: 150
    };

    try {
      // Statistiques des prescriptions
      const prescriptionStats = await sequelize.query(
        `SELECT
          COUNT(*) as total_prescriptions,
          COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as prescriptions_mois,
          COUNT(*) FILTER (WHERE statut IN ('en_attente', 'en_cours')) as prescriptions_actives
        FROM prescriptions
        WHERE veterinaire_id = $1`,
        {
          bind: [veterinaireId],
          type: QueryTypes.SELECT
        }
      );

      // Statistiques des consultations
      const consultationStats = await sequelize.query(
        `SELECT
          COUNT(*) as total_consultations,
          COUNT(*) FILTER (WHERE date >= NOW() - INTERVAL '30 days') as consultations_mois,
          COUNT(*) FILTER (WHERE date >= NOW() AND date <= NOW() + INTERVAL '7 days') as consultations_semaine_prochaine,
          COUNT(DISTINCT eleveur_id) as eleveurs_suivis
        FROM consultations
        WHERE veterinaire_id = $1`,
        {
          bind: [veterinaireId],
          type: QueryTypes.SELECT
        }
      );

      if (prescriptionStats[0]) {
        stats = { ...stats, ...prescriptionStats[0] };
      }
      if (consultationStats[0]) {
        stats = { ...stats, ...consultationStats[0] };
      }
    } catch (error) {
      console.error('Erreur lors du calcul des statistiques:', error);
      // Continue with default stats
    }

    // Consultations à venir (prochaines 7 jours)
    const consultationsAVenirQuery =
      "SELECT " +
      "  c.*, " +
      "  e.nom as eleveur_nom, " +
      "  e.prenom as eleveur_prenom, " +
      "  e.telephone as eleveur_telephone, " +
      "  e.adresse as eleveur_adresse " +
      "FROM consultations c " +
      "JOIN eleveurs e ON c.eleveur_id = e.id " +
      "WHERE c.veterinaire_id = :veterinaireId " +
      "  AND c.date >= NOW() " +
      "  AND c.date <= NOW() + INTERVAL '7 days' " +
      "  AND c.statut != 'annulee' " +
      "ORDER BY c.date ASC " +
      "LIMIT 10";

    // Dernières prescriptions avec détails
    const prescriptionsRecentesQuery =
      "SELECT " +
      "  p.*, " +
      "  e.nom as eleveur_nom, " +
      "  e.prenom as eleveur_prenom, " +
      "  e.telephone as eleveur_telephone, " +
      "  v.espece, " +
      "  v.race, " +
      "  v.quantite as nombre_animaux " +
      "FROM prescriptions p " +
      "JOIN eleveurs e ON p.eleveur_id = e.id " +
      "LEFT JOIN volailles v ON p.volaille_id = v.id " +
      "WHERE p.veterinaire_id = :veterinaireId " +
      "ORDER BY p.created_at DESC " +
      "LIMIT 10";

    // Historique des consultations récentes
    const consultationsHistoriqueQuery =
      "SELECT " +
      "  c.*, " +
      "  e.nom as eleveur_nom, " +
      "  e.prenom as eleveur_prenom, " +
      "  e.telephone as eleveur_telephone " +
      "FROM consultations c " +
      "JOIN eleveurs e ON c.eleveur_id = e.id " +
      "WHERE c.veterinaire_id = :veterinaireId " +
      "  AND c.date <= NOW() " +
      "  AND c.statut = 'terminee' " +
      "ORDER BY c.date DESC " +
      "LIMIT 10";

    // Graphiques - Consultations par mois (6 derniers mois)
    const consultationsGraphiqueQuery =
      "SELECT " +
      "  DATE_TRUNC('month', c.date) as mois, " +
      "  COUNT(*) as nombre_consultations, " +
      "  COUNT(*) FILTER (WHERE c.statut = 'terminee') as consultations_terminees, " +
      "  AVG(CASE WHEN c.note_satisfaction IS NOT NULL THEN c.note_satisfaction END) as satisfaction_moyenne " +
      "FROM consultations c " +
      "WHERE c.veterinaire_id = :veterinaireId " +
      "  AND c.date >= NOW() - INTERVAL '6 months' " +
      "GROUP BY DATE_TRUNC('month', c.date) " +
      "ORDER BY mois DESC";

    // Types de consultations les plus fréquents
    const typesConsultationsQuery =
      "SELECT " +
      "  COALESCE(c.symptomes, 'Non spécifié') as motif, " +
      "  COUNT(*) as nombre, " +
      "  AVG(CASE WHEN c.note_satisfaction IS NOT NULL THEN c.note_satisfaction END) as satisfaction_moyenne " +
      "FROM consultations c " +
      "WHERE c.veterinaire_id = :veterinaireId " +
      "  AND c.date >= NOW() - INTERVAL '6 months' " +
      "  AND c.statut = 'terminee' " +
      "GROUP BY c.symptomes " +
      "ORDER BY nombre DESC " +
      "LIMIT 5";

    // Alertes santé récentes
    const alertesSanteQuery =
      "SELECT " +
      "  a.*, " +
      "  e.nom as eleveur_nom, " +
      "  e.prenom as eleveur_prenom, " +
      "  e.telephone as eleveur_telephone, " +
      "  v.espece, " +
      "  v.race, " +
      "  v.age, " +
      "  v.poids, " +
      "  v.sante->>'taux_mortalite' as taux_mortalite, " +
      "  v.sante->>'mortalite_semaine' as mortalite_semaine " +
      "FROM alertes_stock a " +
      "JOIN eleveurs e ON a.eleveur_id = e.id " +
      "LEFT JOIN volailles v ON a.volaille_id = v.id " +
      "WHERE a.eleveur_id IN (SELECT eleveur_id FROM consultations WHERE veterinaire_id = :veterinaireId) " +
      "  AND ( " +
      "    v.alerte_active = true " +
      "    OR CAST(v.sante->>'taux_mortalite' AS DECIMAL) > 5 " +
      "    OR CAST(v.sante->>'mortalite_semaine' AS DECIMAL) > 2 " +
      "  ) " +
      "  AND a.statut = 'active' " +
      "GROUP BY a.id, e.id, v.id " +
      "ORDER BY CAST(v.sante->>'taux_mortalite' AS DECIMAL) DESC, a.created_at DESC " +
      "LIMIT 5";

    // Exécution des requêtes en parallèle
    try {
      const [
        consultationsAVenir,
        prescriptionsRecentes,
        consultationsHistorique,
        consultationsGraphique,
        typesConsultations,
        alertesSante
      ] = await Promise.all([
        sequelize.query(consultationsAVenirQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        }),
        sequelize.query(prescriptionsRecentesQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        }),
        sequelize.query(consultationsHistoriqueQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        }),
        sequelize.query(consultationsGraphiqueQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        }),
        sequelize.query(typesConsultationsQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        }),
        sequelize.query(alertesSanteQuery, {
          replacements: { veterinaireId },
          type: QueryTypes.SELECT
        })
      ]);

      const statsResult = await sequelize.query(statsQuery, {
        replacements: { veterinaireId },
        type: QueryTypes.SELECT
      });
      const stats = statsResult[0] || {};

      res.json({
        status: 'success',
        data: {
          stats: {
            totalPrescriptions: parseInt(stats.total_prescriptions) || 0,
            prescriptionsMois: parseInt(stats.prescriptions_mois) || 0,
            totalConsultations: parseInt(stats.total_consultations) || 0,
            consultationsMois: parseInt(stats.consultations_mois) || 0,
            eleveursSuivis: parseInt(stats.eleveurs_suivis) || 0,
            consultationsSemaineProchaine: parseInt(stats.consultations_semaine_prochaine) || 0,
            prescriptionsActives: parseInt(stats.prescriptions_actives) || 0,
            satisfactionMoyenne: parseFloat(stats.satisfaction_moyenne) || 0,
            coutMoyenConsultation: parseFloat(stats.cout_moyen_consultation) || 0
          },
          consultationsAVenir,
          prescriptionsRecentes,
          consultationsHistorique,
          graphiques: {
            consultationsParMois: consultationsGraphique,
            typesConsultations
          },
          alertesSante
        }
      });

    } catch (error) {
      console.error('Erreur lors de l\'exécution des requêtes du dashboard:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erreur serveur lors de la récupération des données du dashboard',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  } catch (error) {
    console.error('Erreur générale dans le dashboard:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors du chargement du dashboard',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/veterinaire/notifications
// @desc    Get veterinaire notifications and alerts
router.get("/notifications", auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const notificationsQuery =
      "SELECT " +
      "  'consultation' as type, " +
      "  c.id, " +
      "  c.date as date, " +
      "  CONCAT('Consultation programmée avec ', e.nom, ' ', e.prenom) as message, " +
      "  c.symptomes as details, " +
      "  'info' as priorite " +
      "FROM consultations c " +
      "JOIN eleveurs e ON c.eleveur_id = e.id " +
      "WHERE c.veterinaire_id = :veterinaireId " +
      "  AND c.date >= NOW() " +
      "  AND c.date <= NOW() + INTERVAL '24 hours' " +
      "  AND c.statut = 'programmee' " +
      "" +
      "UNION ALL " +
      "" +
      "SELECT " +
      "  'alerte' as type, " +
      "  a.id, " +
      "  a.created_at as date, " +
      "  a.titre as message, " +
      "  CONCAT( " +
      "    a.message, " +
      "    CASE " +
      "      WHEN v.sante->>'taux_mortalite' IS NOT NULL AND CAST(v.sante->>'taux_mortalite' AS DECIMAL) > 5 " +
      "      THEN ' (Taux de mortalite: ' || v.sante->>'taux_mortalite' || '%)' " +
      "      ELSE '' " +
      "    END " +
      "  ) as details, " +
      "  a.priorite " +
      "FROM alertes_stock a " +
      "JOIN eleveurs e ON a.eleveur_id = e.id " +
      "LEFT JOIN volailles v ON a.volaille_id = v.id " +
      "WHERE a.eleveur_id IN (SELECT eleveur_id FROM consultations WHERE veterinaire_id = :veterinaireId) " +
      "  AND a.type_alerte IN ('mortalite_elevee', 'stock_medicament_bas', 'temperature_anormale', 'stock_aliment_bas') " +
      "  AND a.statut = 'active' " +
      "  AND a.created_at >= NOW() - INTERVAL '7 days' " +
      "ORDER BY id, date DESC " +
      "LIMIT 20";

    const notifications = await sequelize.query(notificationsQuery, {
      replacements: { veterinaireId },
      type: QueryTypes.SELECT
    });

    res.status(200).json({
      status: 'success',
      data: {
        notifications,
        count: notifications.length
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la récupération des notifications',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/veterinaire/consultations/quick
// @desc    Quick action - Schedule new consultation
// @access  Private/Veterinaire
router.post('/consultations/quick', auth, isVeterinaire, async (req, res) => {
  try {
    const {
      eleveur_id,
      date_consultation,
      motif,
      urgence = false
    } = req.body;

    const veterinaireId = req.user.id;

    const insertQuery =
      "INSERT INTO consultations ( " +
      "  veterinaire_id, eleveur_id, date, " +
      "  symptomes, statut, urgence, created_at, updated_at " +
      ") VALUES (:veterinaireId, :eleveur_id, :date_consultation, :motif, 'programmee', :urgence, NOW(), NOW()) " +
      "RETURNING *";

    const result = await sequelize.query(insertQuery, {
      replacements: { veterinaireId, eleveur_id, date_consultation, motif, urgence },
      type: QueryTypes.INSERT
    });

    res.status(201).json({
      status: 'success',
      message: 'Consultation programmée avec succès',
      data: {
        consultation: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la programmation rapide de consultation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la programmation de consultation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/veterinaire/prescriptions/quick
// @desc    Quick action - Create new prescription
// @access  Private/Veterinaire
router.post('/prescriptions/quick', auth, isVeterinaire, async (req, res) => {
  try {
    const {
      eleveur_id,
      volaille_id,
      medicament,
      dosage,
      duree_traitement,
      instructions
    } = req.body;

    const veterinaireId = req.user.id;

    // Générer un numéro de prescription unique
    const datePart = new Date().toISOString().slice(0,10).replace(/-/g, '');
    const randomPart = Math.floor(10000 + Math.random() * 90000);
    const numero_prescription = 'RX-' + datePart + '-' + randomPart;

    const insertQuery =
      "INSERT INTO prescriptions ( " +
      "  veterinaire_id, eleveur_id, volaille_id, " +
      "  numero_prescription, diagnostic, posologie, " +
      "  duree_traitement, medicaments, statut, created_at, updated_at " +
      ") VALUES ( " +
      "  :veterinaireId, :eleveur_id, :volaille_id, " +
      "  :numero_prescription, :instructions, :dosage, " +
      "  :duree_traitement, :medicaments, 'en_attente', NOW(), NOW() " +
      ") " +
      "RETURNING *";

    const result = await sequelize.query(insertQuery, {
      replacements: {
        veterinaireId,
        eleveur_id,
        volaille_id,
        numero_prescription,
        instructions,
        dosage,
        duree_traitement,
        medicaments: JSON.stringify([{ nom: medicament, dosage, instructions }])
      },
      type: QueryTypes.INSERT
    });

    res.status(201).json({
      status: 'success',
      message: 'Prescription créée avec succès',
      data: {
        prescription: result[0][0]
      }
    });

  } catch (error) {
    console.error('Erreur lors de la création rapide de prescription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur serveur lors de la création de prescription',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/veterinaire/prescriptions
// @desc    Get prescriptions for current veterinaire
// @access  Private/Veterinaire
router.get('/prescriptions', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const prescriptions = await sequelize.query(
      `SELECT
        p.id,
        p.numero_prescription,
        p.diagnostic,
        p.medicaments,
        p.posologie,
        p.duree_traitement,
        p.statut,
        p.created_at,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone,
        v.type_volaille,
        v.race
      FROM prescriptions p
      LEFT JOIN eleveurs e ON p.eleveur_id = e.id
      LEFT JOIN volailles v ON p.volaille_id = v.id
      WHERE p.veterinaire_id = $1
      ORDER BY p.created_at DESC`,
      {
        bind: [veterinaireId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: prescriptions,
      count: prescriptions.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des prescriptions:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des prescriptions'
    });
  }
});

// @route   GET /api/veterinaire/prescriptions/stats
// @desc    Get prescription statistics
// @access  Private/Veterinaire
router.get('/prescriptions/stats', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const stats = await sequelize.query(
      `SELECT
        COUNT(*) as total_prescriptions,
        COUNT(*) FILTER (WHERE statut = 'en_attente') as prescriptions_en_attente,
        COUNT(*) FILTER (WHERE statut = 'validee') as prescriptions_validees,
        COUNT(*) FILTER (WHERE statut = 'terminee') as prescriptions_terminees,
        COUNT(*) FILTER (WHERE suivi_requis = true) as prescriptions_suivi_requis,
        COUNT(DISTINCT eleveur_id) as eleveurs_traites
      FROM prescriptions
      WHERE veterinaire_id = $1`,
      {
        bind: [veterinaireId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: stats[0]
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de prescriptions:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des statistiques de prescriptions'
    });
  }
});

// @route   GET /api/veterinaire/patients
// @desc    Get patients (eleveurs) for current veterinaire
// @access  Private/Veterinaire
router.get('/patients', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const patients = await sequelize.query(
      `SELECT DISTINCT
        e.id,
        e.nom,
        e.prenom,
        e.telephone,
        e.email,
        e.adresse,
        e.nom_exploitation,
        COUNT(DISTINCT c.id) as total_consultations,
        COUNT(DISTINCT p.id) as total_prescriptions,
        MAX(c.date) as derniere_consultation
      FROM eleveurs e
      LEFT JOIN consultations c ON e.id = c.eleveur_id AND c.veterinaire_id = $1
      LEFT JOIN prescriptions p ON e.id = p.eleveur_id AND p.veterinaire_id = $1
      WHERE (c.veterinaire_id = $1 OR p.veterinaire_id = $1)
      GROUP BY e.id, e.nom, e.prenom, e.telephone, e.email, e.adresse, e.nom_exploitation
      ORDER BY derniere_consultation DESC NULLS LAST`,
      {
        bind: [veterinaireId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: patients,
      count: patients.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des patients:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des patients'
    });
  }
});

// @route   GET /api/veterinaire/medicaments
// @desc    Get available medications
// @access  Private/Veterinaire
router.get('/medicaments', auth, isVeterinaire, async (req, res) => {
  try {
    // Mock medication data - replace with actual database
    const medicaments = [
      {
        id: 1,
        nom: 'Amoxicilline',
        type: 'Antibiotique',
        forme: 'Comprimé',
        dosage_recommande: '10mg/kg',
        duree_traitement: '7-10 jours',
        contre_indications: 'Allergie aux pénicillines'
      },
      {
        id: 2,
        nom: 'Ivermectine',
        type: 'Antiparasitaire',
        forme: 'Solution injectable',
        dosage_recommande: '0.2mg/kg',
        duree_traitement: 'Dose unique',
        contre_indications: 'Jeunes animaux < 6 semaines'
      },
      {
        id: 3,
        nom: 'Dexaméthasone',
        type: 'Anti-inflammatoire',
        forme: 'Solution injectable',
        dosage_recommande: '0.5-2mg/kg',
        duree_traitement: '3-5 jours',
        contre_indications: 'Infections virales, gestation'
      }
    ];

    res.json({
      status: 'success',
      data: medicaments,
      count: medicaments.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des médicaments:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des médicaments'
    });
  }
});

// @route   GET /api/veterinaire/consultations
// @desc    Get consultations for current veterinaire
// @access  Private/Veterinaire
router.get('/consultations', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const consultations = await sequelize.query(
      `SELECT
        c.id,
        c.date,
        c.heure,
        c.motif,
        c.diagnostic,
        c.traitement,
        c.statut,
        c.urgence,
        c.created_at,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone,
        v.type_volaille,
        v.race,
        v.age_semaines
      FROM consultations c
      LEFT JOIN eleveurs e ON c.eleveur_id = e.id
      LEFT JOIN volailles v ON c.volaille_id = v.id
      WHERE c.veterinaire_id = $1
      ORDER BY c.date DESC, c.heure DESC`,
      {
        bind: [veterinaireId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: consultations,
      count: consultations.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des consultations:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des consultations'
    });
  }
});

// @route   GET /api/veterinaire/consultations/stats
// @desc    Get consultation statistics
// @access  Private/Veterinaire
router.get('/consultations/stats', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const stats = await sequelize.query(
      `SELECT
        COUNT(*) as total_consultations,
        COUNT(*) FILTER (WHERE date = CURRENT_DATE) as consultations_aujourdhui,
        COUNT(*) FILTER (WHERE urgence = true) as consultations_urgentes,
        COUNT(*) FILTER (WHERE statut = 'terminee') as consultations_terminees,
        COUNT(*) FILTER (WHERE statut = 'programmee') as consultations_programmees,
        COUNT(*) FILTER (WHERE statut = 'en_cours') as consultations_en_cours
      FROM consultations
      WHERE veterinaire_id = $1`,
      {
        bind: [veterinaireId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: stats[0]
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de consultations:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des statistiques de consultations'
    });
  }
});

// @route   GET /api/veterinaire/appointments
// @desc    Get appointments for current veterinaire
// @access  Private/Veterinaire
router.get('/appointments', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    const appointments = await sequelize.query(
      `SELECT
        c.id,
        c.date,
        c.heure,
        c.motif,
        c.statut,
        c.urgence,
        e.nom as eleveur_nom,
        e.prenom as eleveur_prenom,
        e.telephone as eleveur_telephone,
        e.email as eleveur_email
      FROM consultations c
      LEFT JOIN eleveurs e ON c.eleveur_id = e.id
      WHERE c.veterinaire_id = $1
        AND c.statut IN ('programmee', 'confirmee')
        AND c.date >= CURRENT_DATE
      ORDER BY c.date ASC, c.heure ASC`,
      {
        bind: [veterinaireId],
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: appointments,
      count: appointments.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des rendez-vous:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des rendez-vous'
    });
  }
});

// @route   GET /api/veterinaire/eleveurs
// @desc    Get all eleveurs for veterinaire
// @access  Private/Veterinaire
router.get('/eleveurs', auth, isVeterinaire, async (req, res) => {
  try {
    const eleveurs = await sequelize.query(
      `SELECT
        e.id,
        e.nom,
        e.prenom,
        e.telephone,
        e.email,
        e.adresse,
        e.nom_exploitation,
        e.type_elevage,
        e.capacite_max,
        COUNT(DISTINCT v.id) as nombre_volailles
      FROM eleveurs e
      LEFT JOIN volailles v ON e.id = v.eleveur_id
      GROUP BY e.id, e.nom, e.prenom, e.telephone, e.email, e.adresse, e.nom_exploitation, e.type_elevage, e.capacite_max
      ORDER BY e.nom, e.prenom`,
      {
        type: QueryTypes.SELECT
      }
    );

    res.json({
      status: 'success',
      data: eleveurs,
      count: eleveurs.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des éleveurs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des éleveurs'
    });
  }
});

// @route   GET /api/veterinaire/notifications
// @desc    Get notifications for current veterinaire
// @access  Private/Veterinaire
router.get('/notifications', auth, isVeterinaire, async (req, res) => {
  try {
    const veterinaireId = req.user.id;

    // Mock notifications - replace with actual database queries
    const notifications = [
      {
        id: 1,
        type: 'consultation_urgente',
        title: 'Consultation urgente demandée',
        message: 'M. Benali demande une consultation urgente pour ses poules pondeuses',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false,
        priority: 'high'
      },
      {
        id: 2,
        type: 'prescription_suivi',
        title: 'Suivi de prescription requis',
        message: 'Prescription #RX-20250108-12345 nécessite un suivi',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        read: false,
        priority: 'medium'
      },
      {
        id: 3,
        type: 'rendez_vous',
        title: 'Rendez-vous confirmé',
        message: 'Rendez-vous avec Mme Kaci confirmé pour demain 14h',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true,
        priority: 'low'
      }
    ];

    res.json({
      status: 'success',
      data: notifications,
      count: notifications.length,
      unread_count: notifications.filter(n => !n.read).length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la récupération des notifications'
    });
  }
});

module.exports = router;
