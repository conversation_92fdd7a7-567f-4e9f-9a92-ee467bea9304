# SYSTEMATIC ERROR RESOLUTION - PROGRESS LOG
# Started: 2025-07-10 17:55:00
# Status: IN PROGRESS

## ✅ PHASE 1 COMPLETED (2025-07-10 17:55:00)
# FIXED: Database schema issues
# - Added missing serviceName column to ApiConfigs table
# - Fixed database connection and model loading
# - Backend server now running successfully on port 3003
# - All models loaded and associations set correctly

## 🔄 PHASE 2: IN PROGRESS - API Authentication & 500 Errors
# Next: Fix 403 Forbidden and 500 Internal Server errors

# REMAINING ERRORS TO FIX:
http://localhost:5174/eleveur/dashboard
console errors :

👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:5174/api/eleveurs/2/dashboard
[HTTP/1.1 403 Forbidden 4590ms]

XHRGET
http://localhost:5174/api/eleveurs/2/ouvriers
[HTTP/1.1 403 Forbidden 4610ms]

Erreur lors de la récupération des données:
Object { message: "Request failed with status code 403", name: "AxiosError", code: "ERR_BAD_REQUEST", config: {…}, request: XMLHttpRequest, response: {…}, status: 403, stack: "", … }
<anonymous code>:1:145535




http://localhost:5174/eleveur/volailles

XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 116ms]

🔄 Tentative 3/3 pour /volailles axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 45ms]

🔄 Tentative 3/3 pour /eleveurs axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 259ms]

XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 341ms]

🔄 Tentative 3/3 pour /eleveurs axiosConfig.js:78:15
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
🔄 Tentative 3/3 pour /volailles axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 166ms]

XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 617ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:35:53.123Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des volailles", url: "/volailles", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des volailles: StandardError: Erreur lors de la récupération des volailles
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    fetchVolailles VolaillesList.jsx:49
    VolaillesList VolaillesList.jsx:43
    React 13
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/eleveurs
[HTTP/1.1 500 Internal Server Error 646ms]

XHRGET
http://localhost:3003/api/volailles
[HTTP/1.1 500 Internal Server Error 773ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:35:53.741Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur serveur lors de la récupération des éleveurs", url: "/eleveurs", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des éleveurs: StandardError: Erreur serveur lors de la récupération des éleveurs
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    fetchEleveurs VolaillesList.jsx:61
    VolaillesList VolaillesList.jsx:44
    React 13
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:35:54.091Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur serveur lors de la récupération des éleveurs", url: "/eleveurs", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des éleveurs: StandardError: Erreur serveur lors de la récupération des éleveurs
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    fetchEleveurs VolaillesList.jsx:61
    VolaillesList VolaillesList.jsx:44
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:35:54.418Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des volailles", url: "/volailles", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur lors de la récupération des volailles: StandardError: Erreur lors de la récupération des volailles
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    fetchVolailles VolaillesList.jsx:49
    VolaillesList VolaillesList.jsx:43
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535



http://localhost:5174/eleveur/ventes
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 39ms]

XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 77ms]

🔄 Tentative 1/3 pour /ventes/eleveur/2 2 axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 82ms]

XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 229ms]

🔄 Tentative 2/3 pour /ventes/eleveur/2 2 axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 123ms]

🔄 Tentative 3/3 pour /ventes/eleveur/2 axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 119ms]

🔄 Tentative 3/3 pour /ventes/eleveur/2 axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 113ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:36:20.487Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des ventes", url: "/ventes/eleveur/2", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur ventes: StandardError: Erreur lors de la récupération des ventes
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    getVentes eleveurService.js:12
    loadVentes VentesManagement.jsx:87
    VentesManagement VentesManagement.jsx:80
    React 13
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
Uncaught TypeError: can't access property "toLocaleString", stats.chiffreAffaires is undefined
    VentesManagement VentesManagement.jsx:256
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
VentesManagement.jsx:308:13
Uncaught TypeError: can't access property "toLocaleString", stats.chiffreAffaires is undefined
    VentesManagement VentesManagement.jsx:256
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
VentesManagement.jsx:308:13
The above error occurred in the <VentesManagement> component:

VentesManagement@http://localhost:5174/src/pages/eleveur/VentesManagement.jsx:67:20
Suspense
RenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7
Outlet@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4495:20
div
node_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49
Box3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35
main
node_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49
Box3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35
div
node_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49
Box3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35
DashboardLayout@http://localhost:5174/src/layouts/DashboardLayout.jsx:31:25
RenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7
Routes@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4561:7
Router@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4509:7
BrowserRouter@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:5252:7
GlobalErrorHandler@http://localhost:5174/src/components/errors/GlobalErrorHandler.jsx:24:28
LanguageProvider@http://localhost:5174/src/contexts/LanguageContext.jsx:27:33
AuthProvider@http://localhost:5174/src/contexts/AuthContext.jsx:29:29
DefaultPropsProvider@http://localhost:5174/node_modules/.vite/deps/chunk-SPILCDBC.js?v=7607177c:3279:30
RtlProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1138:7
ThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1089:7
ThemeProvider2@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1180:7
ThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-QXXVNB4I.js?v=7607177c:332:7
ErrorBoundary@http://localhost:5174/src/components/ErrorBoundary.jsx:12:5
App

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary. <anonymous code>:1:145535
ErrorBoundary a capturé une erreur: TypeError: can't access property "toLocaleString", stats.chiffreAffaires is undefined
    VentesManagement VentesManagement.jsx:256
    React 9
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38

Object { componentStack: "\nVentesManagement@http://localhost:5174/src/pages/eleveur/VentesManagement.jsx:67:20\nSuspense\nRenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7\nOutlet@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4495:20\ndiv\nnode_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49\nBox3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35\nmain\nnode_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49\nBox3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35\ndiv\nnode_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49\nBox3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35\nDashboardLayout@http://localhost:5174/src/layouts/DashboardLayout.jsx:31:25\nRenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7\nRoutes@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4561:7\nRouter@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4509:7\nBrowserRouter@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:5252:7\nGlobalErrorHandler@http://localhost:5174/src/components/errors/GlobalErrorHandler.jsx:24:28\nLanguageProvider@http://localhost:5174/src/contexts/LanguageContext.jsx:27:33\nAuthProvider@http://localhost:5174/src/contexts/AuthContext.jsx:29:29\nDefaultPropsProvider@http://localhost:5174/node_modules/.vite/deps/chunk-SPILCDBC.js?v=7607177c:3279:30\nRtlProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1138:7\nThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1089:7\nThemeProvider2@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1180:7\nThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-QXXVNB4I.js?v=7607177c:332:7\nErrorBoundary@http://localhost:5174/src/components/ErrorBoundary.jsx:12:5\nApp" }
 Component Stack:
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/ventes/eleveur/2
[HTTP/1.1 500 Internal Server Error 211ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:36:21.258Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des ventes", url: "/ventes/eleveur/2", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur ventes: StandardError: Erreur lors de la récupération des ventes
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:252
    <anonymous> axiosConfig.js:118
    promise callback*_request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    wrap bind.js:5
    <anonymous> axiosConfig.js:131
    _request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    getVentes eleveurService.js:12
    loadVentes VentesManagement.jsx:87
    VentesManagement VentesManagement.jsx:80
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535






http://localhost:5174/eleveur/statistics :

👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:38:37.384Z", type: "NETWORK", code: "SERVER_UNAVAILABLE", message: "Serveur temporairement indisponible. Veuillez réessayer dans quelques instants.", url: undefined, method: undefined, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Warning: Encountered two children with the same key, `2025-07-10T17:38:37.384Z`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version. Component Stack:
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorList ErrorDisplay.jsx:188
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorDisplay ErrorDisplay.jsx:247
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
Warning: Encountered two children with the same key, `2025-07-10T17:38:37.384Z`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version. Component Stack:
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorList ErrorDisplay.jsx:188
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorDisplay ErrorDisplay.jsx:247
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
Warning: Encountered two children with the same key, `2025-07-10T17:38:37.384Z`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version. Component Stack:
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorList ErrorDisplay.jsx:188
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorDisplay ErrorDisplay.jsx:247
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:38:43.877Z", type: "NETWORK", code: "SERVER_UNAVAILABLE", message: "Serveur temporairement indisponible. Veuillez réessayer dans quelques instants.", url: undefined, method: undefined, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Warning: Encountered two children with the same key, `2025-07-10T17:38:43.877Z`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version. Component Stack:
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorList ErrorDisplay.jsx:188
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorDisplay ErrorDisplay.jsx:247
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
Warning: Encountered two children with the same key, `2025-07-10T17:38:43.877Z`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version. Component Stack:
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorList ErrorDisplay.jsx:188
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorDisplay ErrorDisplay.jsx:247
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
Warning: Encountered two children with the same key, `2025-07-10T17:38:43.877Z`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version. Component Stack:
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorList ErrorDisplay.jsx:188
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    ErrorDisplay ErrorDisplay.jsx:247
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535




http://localhost:5174/eleveur/weather
[vite] connecting... client:789:9
[vite] connected. client:912:15
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
⚠️ Seuil de performance dépassé: render_time (5937 > 16) <anonymous code>:1:145535
✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 99ms]

XHRGET
http://localhost:3003/api/integrations/weather/recommendations?farm_id=null
[HTTP/1.1 404 Not Found 260ms]

XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 279ms]

XHRGET
http://localhost:3003/api/integrations/weather/recommendations?farm_id=null
[HTTP/1.1 404 Not Found 597ms]

🔄 Tentative 1/3 pour /integrations/weather/current?farm_id=null 2 axiosConfig.js:78:15
🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:39:48.856Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/integrations/weather/recommendations?farm_id=null", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur météo: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    getRecommendations weatherService.js:58
    loadWeatherData WeatherDashboard.jsx:72
    WeatherDashboard WeatherDashboard.jsx:54
    React 13
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
react-i18next:: useTranslation: You will need to pass in an i18next instance by using initReactI18next
Object { code: "NO_I18NEXT_INSTANCE" }
 Component Stack:
    WeatherCard WeatherCard.jsx:38
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Grid2 Grid.js:367
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Grid2 Grid.js:367
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Container3 createContainer.js:101
    WeatherDashboard WeatherDashboard.jsx:44
    Suspense unknown:0
    RenderedRoute hooks.tsx:665
    Outlet components.tsx:341
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    main unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    div unknown:0
    node_modules chunk-JMCQEFQL.js:1727
    Box3 createBox.js:23
    DashboardLayout DashboardLayout.jsx:17
    RenderedRoute hooks.tsx:665
    Routes components.tsx:514
    Router components.tsx:428
    BrowserRouter index.tsx:793
    GlobalErrorHandler GlobalErrorHandler.jsx:11
    LanguageProvider LanguageContext.jsx:9
    AuthProvider AuthContext.jsx:11
    DefaultPropsProvider DefaultPropsProvider.js:8
    RtlProvider index.js:11
    ThemeProvider ThemeProvider.js:33
    ThemeProvider2 ThemeProvider.js:47
    ThemeProvider ThemeProvider.js:14
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:39:49.534Z", type: "NOT_FOUND", code: "RESOURCE_NOT_FOUND", message: "La ressource demandée est introuvable.", url: "/integrations/weather/recommendations?farm_id=null", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
Erreur météo: StandardError: La ressource demandée est introuvable.
    StandardError errorHandler.js:50
    handleAxiosError errorHandler.js:219
    <anonymous> axiosConfig.js:135
    promise callback*_request Axios.js:163
    request Axios.js:40
    method Axios.js:213
    wrap bind.js:5
    getRecommendations weatherService.js:58
    loadWeatherData WeatherDashboard.jsx:72
    WeatherDashboard WeatherDashboard.jsx:54
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 330ms]

XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 948ms]

Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:5174/eleveur/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
🔄 Tentative 2/3 pour /integrations/weather/current?farm_id=null 2 axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 198ms]

🔄 Tentative 3/3 pour /integrations/weather/current?farm_id=null axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 41ms]

🔄 Tentative 3/3 pour /integrations/weather/current?farm_id=null axiosConfig.js:78:15
XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 28ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:39:55.484Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des données météo", url: "/integrations/weather/current?farm_id=null", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535
XHRGET
http://localhost:3003/api/integrations/weather/current?farm_id=null
[HTTP/1.1 500 Internal Server Error 167ms]

🚨 Erreur capturée:
Object { timestamp: "2025-07-10T17:39:56.114Z", type: "SERVER", code: "SERVER_UNAVAILABLE", message: "Erreur lors de la récupération des données météo", url: "/integrations/weather/current?farm_id=null", method: "get", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", connectionStatus: true }
<anonymous code>:1:145535



http://localhost:5174/eleveur/iot

[vite] connecting... client:789:9
[vite] connected. client:912:15
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
⚠️ Seuil de performance dépassé: render_time (6375 > 16) <anonymous code>:1:145535
✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
Uncaught TypeError: devices.map is not a function
    IoTDevicesManagement IoTDevicesManagement.jsx:376
    React 11
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
IoTDevicesManagement.jsx:583:63
Uncaught TypeError: devices.map is not a function
    IoTDevicesManagement IoTDevicesManagement.jsx:376
    React 12
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38
IoTDevicesManagement.jsx:583:63
The above error occurred in the <IoTDevicesManagement> component:

IoTDevicesManagement@http://localhost:5174/src/pages/eleveur/IoTDevicesManagement.jsx:71:20
Suspense
RenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7
Outlet@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4495:20
div
node_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49
Box3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35
main
node_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49
Box3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35
div
node_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49
Box3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35
DashboardLayout@http://localhost:5174/src/layouts/DashboardLayout.jsx:31:25
RenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7
Routes@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4561:7
Router@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4509:7
BrowserRouter@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:5252:7
GlobalErrorHandler@http://localhost:5174/src/components/errors/GlobalErrorHandler.jsx:24:28
LanguageProvider@http://localhost:5174/src/contexts/LanguageContext.jsx:27:33
AuthProvider@http://localhost:5174/src/contexts/AuthContext.jsx:29:29
DefaultPropsProvider@http://localhost:5174/node_modules/.vite/deps/chunk-SPILCDBC.js?v=7607177c:3279:30
RtlProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1138:7
ThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1089:7
ThemeProvider2@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1180:7
ThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-QXXVNB4I.js?v=7607177c:332:7
ErrorBoundary@http://localhost:5174/src/components/ErrorBoundary.jsx:12:5
App

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary. <anonymous code>:1:145535
ErrorBoundary a capturé une erreur: TypeError: devices.map is not a function
    IoTDevicesManagement IoTDevicesManagement.jsx:376
    React 9
    workLoop scheduler.development.js:266
    flushWork scheduler.development.js:239
    performWorkUntilDeadline scheduler.development.js:533
    js scheduler.development.js:571
    js scheduler.development.js:633
    __require2 chunk-LK32TJAX.js:18
    js index.js:6
    __require2 chunk-LK32TJAX.js:18
    React 2
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    js React
    __require2 chunk-LK32TJAX.js:18
    <anonymous> react-dom_client.js:38

Object { componentStack: "\nIoTDevicesManagement@http://localhost:5174/src/pages/eleveur/IoTDevicesManagement.jsx:71:20\nSuspense\nRenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7\nOutlet@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4495:20\ndiv\nnode_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49\nBox3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35\nmain\nnode_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49\nBox3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35\ndiv\nnode_modules/@emotion/react/dist/emotion-element-489459f2.browser.development.esm.js/withEmotionCache2/<@http://localhost:5174/node_modules/.vite/deps/chunk-JMCQEFQL.js?v=7607177c:1727:49\nBox3@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:398:35\nDashboardLayout@http://localhost:5174/src/layouts/DashboardLayout.jsx:31:25\nRenderedRoute@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4092:7\nRoutes@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4561:7\nRouter@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:4509:7\nBrowserRouter@http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=7607177c:5252:7\nGlobalErrorHandler@http://localhost:5174/src/components/errors/GlobalErrorHandler.jsx:24:28\nLanguageProvider@http://localhost:5174/src/contexts/LanguageContext.jsx:27:33\nAuthProvider@http://localhost:5174/src/contexts/AuthContext.jsx:29:29\nDefaultPropsProvider@http://localhost:5174/node_modules/.vite/deps/chunk-SPILCDBC.js?v=7607177c:3279:30\nRtlProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1138:7\nThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1089:7\nThemeProvider2@http://localhost:5174/node_modules/.vite/deps/chunk-6FAYWNJC.js?v=7607177c:1180:7\nThemeProvider@http://localhost:5174/node_modules/.vite/deps/chunk-QXXVNB4I.js?v=7607177c:332:7\nErrorBoundary@http://localhost:5174/src/components/ErrorBoundary.jsx:12:5\nApp" }
 Component Stack:
    ErrorBoundary ErrorBoundary.jsx:7
    App unknown:0
<anonymous code>:1:145535
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:5174/eleveur/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
