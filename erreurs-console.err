[vite] connecting... client:789:9
[vite] connected. client:912:15
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
🔍 AuthContext: Chargement utilisateur, token: true AuthContext.jsx:74:15
📡 AuthContext: Appel API /auth/user AuthContext.jsx:81:17
⚠️ Seuil de performance dépassé: render_time (40735 > 16) <anonymous code>:1:145535
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:5174/eleveur/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
🔄 Tentative 1/3 pour /auth/user 2 axiosConfig.js:78:15
✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
XHRGET
http://localhost:5174/api/eleveurs/1/dashboard
[HTTP/1.1 403 Forbidden 8517ms]

XHRGET
http://localhost:5174/api/eleveurs/1/ouvriers
[HTTP/1.1 403 Forbidden 7778ms]

XHRGET
http://localhost:5174/api/eleveurs/1/dashboard
[HTTP/1.1 403 Forbidden 8322ms]

XHRGET
http://localhost:5174/api/eleveurs/1/ouvriers
[HTTP/1.1 403 Forbidden 7103ms]

✅ AuthContext: Utilisateur chargé:
Object { id: 2, username: "eleveur", email: "<EMAIL>", role_id: 15, first_name: "", last_name: "", profile_id: null, status: "active", preferences: {}, firebase_uid: "Uf4xoz4o2Fctt9AFZt3kiomkiWy1", … }
AuthContext.jsx:83:17
🔧 AuthContext: Rôle formaté: eleveur AuthContext.jsx:88:17
🏁 AuthContext: Fin du chargement AuthContext.jsx:99:17
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
DashboardLayout.jsx:83:11
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur DashboardLayout.jsx:100:11
👤 DashboardLayout: Utilisateur authentifié:
Object { id: 2, email: "<EMAIL>", role: "eleveur", requiredRole: "eleveur" }
<anonymous code>:1:148389
✅ DashboardLayout: Accès autorisé pour le rôle: eleveur <anonymous code>:1:148389
Erreur lors de la récupération des données:
Object { message: "Request failed with status code 403", name: "AxiosError", code: "ERR_BAD_REQUEST", config: {…}, request: XMLHttpRequest, response: {…}, status: 403, stack: "", … }
<anonymous code>:1:145535
Erreur lors de la récupération des données:
Object { message: "Request failed with status code 403", name: "AxiosError", code: "ERR_BAD_REQUEST", config: {…}, request: XMLHttpRequest, response: {…}, status: 403, stack: "", … }
<anonymous code>:1:145535
