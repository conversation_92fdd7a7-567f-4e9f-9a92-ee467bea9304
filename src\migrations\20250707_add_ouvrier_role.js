'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Vérifier si le rôle ouvrier existe déjà
    const existingRole = await queryInterface.sequelize.query(
      'SELECT id FROM roles WHERE name = \'ouvrier\'',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (existingRole.length === 0) {
      // Ajouter le rôle ouvrier s'il n'existe pas
      await queryInterface.bulkInsert('roles', [{
        name: 'ouvrier',
        description: 'Rôle pour les ouvriers des éleveurs',
        permissions: JSON.stringify([
          'view_own_profile',
          'update_own_profile',
          'view_assigned_tasks',
          'update_task_status',
          'view_eleveur_data',
          'create_daily_reports'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }]);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Supprimer le rôle ouvrier
    await queryInterface.bulkDelete('roles', { name: 'ouvrier' });
  }
};