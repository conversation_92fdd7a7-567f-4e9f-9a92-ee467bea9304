/**
 * Composant de gestion des ventes pour les éleveurs
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Fab,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  ShoppingCart as CartIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { eleveurAPI } from '../../services/api';

const VentesManagement = () => {
  const { user } = useAuth();
  const [ventes, setVentes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedVente, setSelectedVente] = useState(null);
  const [formData, setFormData] = useState({
    produit_type: '',
    quantite: '',
    prix_unitaire: '',
    client_nom: '',
    client_telephone: '',
    date_vente: new Date(),
    statut: 'en_attente',
    notes: ''
  });

  // Statistiques des ventes
  const [stats, setStats] = useState({
    totalVentes: 0,
    chiffreAffaires: 0,
    ventesEnAttente: 0,
    ventesLivrees: 0
  });

  useEffect(() => {
    loadVentes();
    loadStats();
  }, []);

  const loadVentes = async () => {
    try {
      setLoading(true);
      const response = await eleveurAPI.getVentes(user.id);
      setVentes(response.data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des ventes');
      console.error('Erreur ventes:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await eleveurAPI.getVentesStats(user.id);
      setStats(response.data || stats);
    } catch (err) {
      console.error('Erreur stats ventes:', err);
    }
  };

  const handleOpenDialog = (vente = null) => {
    if (vente) {
      setSelectedVente(vente);
      setFormData({
        produit_type: vente.produit_type || '',
        quantite: vente.quantite || '',
        prix_unitaire: vente.prix_unitaire || '',
        client_nom: vente.client_nom || '',
        client_telephone: vente.client_telephone || '',
        date_vente: new Date(vente.date_vente) || new Date(),
        statut: vente.statut || 'en_attente',
        notes: vente.notes || ''
      });
    } else {
      setSelectedVente(null);
      setFormData({
        produit_type: '',
        quantite: '',
        prix_unitaire: '',
        client_nom: '',
        client_telephone: '',
        date_vente: new Date(),
        statut: 'en_attente',
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedVente(null);
  };

  const handleSubmit = async () => {
    try {
      const venteData = {
        ...formData,
        eleveur_id: user.profile_id,
        montant_total: parseFloat(formData.quantite) * parseFloat(formData.prix_unitaire)
      };

      if (selectedVente) {
        await eleveurAPI.updateVente(selectedVente.id, venteData);
      } else {
        await eleveurAPI.createVente(venteData);
      }

      handleCloseDialog();
      loadVentes();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la vente');
      console.error('Erreur sauvegarde vente:', err);
    }
  };

  const handleDelete = async (venteId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette vente ?')) {
      try {
        await eleveurAPI.deleteVente(venteId);
        loadVentes();
        loadStats();
      } catch (err) {
        setError('Erreur lors de la suppression de la vente');
        console.error('Erreur suppression vente:', err);
      }
    }
  };

  const getStatutColor = (statut) => {
    switch (statut) {
      case 'en_attente': return 'warning';
      case 'confirmee': return 'info';
      case 'livree': return 'success';
      case 'annulee': return 'error';
      default: return 'default';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut) {
      case 'en_attente': return 'En attente';
      case 'confirmee': return 'Confirmée';
      case 'livree': return 'Livrée';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des ventes...</Typography>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1">
            Gestion des Ventes
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvelle Vente
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Cartes de statistiques */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CartIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Ventes
                    </Typography>
                    <Typography variant="h5">
                      {stats.totalVentes}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <MoneyIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Chiffre d'Affaires
                    </Typography>
                    <Typography variant="h5">
                      {stats.chiffreAffaires.toLocaleString()} DA
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUpIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      En Attente
                    </Typography>
                    <Typography variant="h5">
                      {stats.ventesEnAttente}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AssessmentIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Livrées
                    </Typography>
                    <Typography variant="h5">
                      {stats.ventesLivrees}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Table des ventes */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Liste des Ventes
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Produit</TableCell>
                    <TableCell>Quantité</TableCell>
                    <TableCell>Prix Unitaire</TableCell>
                    <TableCell>Montant Total</TableCell>
                    <TableCell>Client</TableCell>
                    <TableCell>Statut</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {ventes.map((vente) => (
                    <TableRow key={vente.id}>
                      <TableCell>
                        {new Date(vente.date_vente).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell>{vente.produit_type}</TableCell>
                      <TableCell>{vente.quantite}</TableCell>
                      <TableCell>{vente.prix_unitaire} DA</TableCell>
                      <TableCell>{vente.montant_total} DA</TableCell>
                      <TableCell>{vente.client_nom}</TableCell>
                      <TableCell>
                        <Chip
                          label={getStatutLabel(vente.statut)}
                          color={getStatutColor(vente.statut)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Modifier">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(vente)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer">
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(vente.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Dialog pour ajouter/modifier une vente */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {selectedVente ? 'Modifier la Vente' : 'Nouvelle Vente'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Type de Produit</InputLabel>
                  <Select
                    value={formData.produit_type}
                    onChange={(e) => setFormData({ ...formData, produit_type: e.target.value })}
                  >
                    <MenuItem value="oeufs">Œufs</MenuItem>
                    <MenuItem value="poulets">Poulets</MenuItem>
                    <MenuItem value="poules">Poules</MenuItem>
                    <MenuItem value="coqs">Coqs</MenuItem>
                    <MenuItem value="poussins">Poussins</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Quantité"
                  type="number"
                  value={formData.quantite}
                  onChange={(e) => setFormData({ ...formData, quantite: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Prix Unitaire (DA)"
                  type="number"
                  value={formData.prix_unitaire}
                  onChange={(e) => setFormData({ ...formData, prix_unitaire: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Date de Vente"
                  value={formData.date_vente}
                  onChange={(date) => setFormData({ ...formData, date_vente: date })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Nom du Client"
                  value={formData.client_nom}
                  onChange={(e) => setFormData({ ...formData, client_nom: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Téléphone du Client"
                  value={formData.client_telephone}
                  onChange={(e) => setFormData({ ...formData, client_telephone: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={formData.statut}
                    onChange={(e) => setFormData({ ...formData, statut: e.target.value })}
                  >
                    <MenuItem value="en_attente">En attente</MenuItem>
                    <MenuItem value="confirmee">Confirmée</MenuItem>
                    <MenuItem value="livree">Livrée</MenuItem>
                    <MenuItem value="annulee">Annulée</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button onClick={handleSubmit} variant="contained">
              {selectedVente ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default VentesManagement;
