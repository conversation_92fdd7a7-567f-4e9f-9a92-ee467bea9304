import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/finance_provider.dart';
import '../../models/transaction_model.dart';

class FinanceDashboardScreen extends ConsumerStatefulWidget {
  final String farmId;

  const FinanceDashboardScreen({Key? key, required this.farmId}) : super(key: key);

  @override
  ConsumerState<FinanceDashboardScreen> createState() => _FinanceDashboardScreenState();
}

class _FinanceDashboardScreenState extends ConsumerState<FinanceDashboardScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(financeProvider);
    final balance = ref.watch(balanceProvider);
    final budgetAllocations = ref.watch(budgetAllocationsProvider);
    final expenses = ref.watch(expensesProvider);

    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(state.error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
              },
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord financier'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBalanceCard(balance),
              const SizedBox(height: 16),
              _buildBudgetOverview(budgetAllocations, expenses),
              const SizedBox(height: 16),
              _buildRecentTransactions(state.transactions),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/finance/add-transaction');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBalanceCard(double balance) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Solde actuel',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Text(
              '${balance.toStringAsFixed(2)} DA',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetOverview(Map<String, double> budgetAllocations, Map<String, double> expenses) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Aperçu du budget',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            ...budgetAllocations.entries.map((entry) {
              final spent = expenses[entry.key] ?? 0.0;
              final percentage = entry.value > 0 ? (spent / entry.value * 100).clamp(0.0, 100.0) : 0.0;
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.key,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey[200],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${spent.toStringAsFixed(2)} / ${entry.value.toStringAsFixed(2)} DA (${percentage.toStringAsFixed(1)}%)',
                    style: const TextStyle(fontSize: 12),
                  ),
                  const SizedBox(height: 12),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions(List<Transaction> transactions) {
    if (transactions.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('Aucune transaction récente'),
          ),
        ),
      );
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Transactions récentes',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/finance/transactions');
                  },
                  child: const Text('Voir tout'),
                ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: transactions.length > 5 ? 5 : transactions.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final transaction = transactions[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: transaction.type == 'income' 
                    ? Colors.green[100] 
                    : Colors.red[100],
                  child: Icon(
                    transaction.type == 'income' 
                      ? Icons.arrow_upward 
                      : Icons.arrow_downward,
                    color: transaction.type == 'income' 
                      ? Colors.green 
                      : Colors.red,
                  ),
                ),
                title: Text(transaction.description),
                subtitle: Text(transaction.category),
                trailing: Text(
                  '${transaction.type == 'income' ? '+' : '-'}${transaction.amount.toStringAsFixed(2)} DA',
                  style: TextStyle(
                    color: transaction.type == 'income' ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    '/finance/edit-transaction',
                    arguments: transaction,
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}