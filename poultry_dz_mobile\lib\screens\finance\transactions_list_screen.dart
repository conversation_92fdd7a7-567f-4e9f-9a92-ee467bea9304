import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/finance_provider.dart';
import '../../models/transaction_model.dart';

class TransactionsListScreen extends ConsumerStatefulWidget {
  final String farmId;

  const TransactionsListScreen({Key? key, required this.farmId}) : super(key: key);

  @override
  ConsumerState<TransactionsListScreen> createState() => _TransactionsListScreenState();
}

class _TransactionsListScreenState extends ConsumerState<TransactionsListScreen> {
  String _searchQuery = '';
  String _selectedType = 'all';
  String _selectedCategory = 'all';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Transaction> _filterTransactions(List<Transaction> transactions) {
    return transactions.where((transaction) {
      final matchesSearch = transaction.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          transaction.category.toLowerCase().contains(_searchQuery.toLowerCase());
      final matchesType = _selectedType == 'all' || transaction.type == _selectedType;
      final matchesCategory = _selectedCategory == 'all' || transaction.category == _selectedCategory;
      return matchesSearch && matchesType && matchesCategory;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(financeProvider);
    final filteredTransactions = _filterTransactions(state.transactions);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Rechercher des transactions...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                            _searchController.clear();
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: state.isLoading
                ? const Center(child: CircularProgressIndicator())
                : state.error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(state.error!),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
                              },
                              child: const Text('Réessayer'),
                            ),
                          ],
                        ),
                      )
                    : filteredTransactions.isEmpty
                        ? const Center(child: Text('Aucune transaction trouvée'))
                        : RefreshIndicator(
                            onRefresh: () async {
                              await ref.read(financeProvider.notifier).loadTransactions(widget.farmId);
                            },
                            child: ListView.separated(
                              itemCount: filteredTransactions.length,
                              separatorBuilder: (context, index) => const Divider(height: 1),
                              itemBuilder: (context, index) {
                                final transaction = filteredTransactions[index];
                                return Dismissible(
                                  key: Key(transaction.id),
                                  direction: DismissDirection.endToStart,
                                  background: Container(
                                    color: Colors.red,
                                    alignment: Alignment.centerRight,
                                    padding: const EdgeInsets.only(right: 16),
                                    child: const Icon(
                                      Icons.delete,
                                      color: Colors.white,
                                    ),
                                  ),
                                  confirmDismiss: (direction) async {
                                    return await showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('Confirmer la suppression'),
                                        content: const Text('Êtes-vous sûr de vouloir supprimer cette transaction ?'),
                                        actions: [
                                          TextButton(
                                            onPressed: () => Navigator.of(context).pop(false),
                                            child: const Text('Annuler'),
                                          ),
                                          TextButton(
                                            onPressed: () => Navigator.of(context).pop(true),
                                            child: const Text('Supprimer'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  onDismissed: (direction) {
                                    ref.read(financeProvider.notifier).deleteTransaction(
                                          transaction.id,
                                          widget.farmId,
                                        );
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(content: Text('Transaction supprimée')),
                                    );
                                  },
                                  child: ListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: transaction.type == 'income'
                                          ? Colors.green[100]
                                          : Colors.red[100],
                                      child: Icon(
                                        transaction.type == 'income'
                                            ? Icons.arrow_upward
                                            : Icons.arrow_downward,
                                        color: transaction.type == 'income'
                                            ? Colors.green
                                            : Colors.red,
                                      ),
                                    ),
                                    title: Text(transaction.description),
                                    subtitle: Text(
                                      '${transaction.category} • ${_formatDate(transaction.date)}',
                                    ),
                                    trailing: Text(
                                      '${transaction.type == 'income' ? '+' : '-'}${transaction.amount.toStringAsFixed(2)} DA',
                                      style: TextStyle(
                                        color: transaction.type == 'income'
                                            ? Colors.green
                                            : Colors.red,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    onTap: () {
                                      Navigator.pushNamed(
                                        context,
                                        '/finance/edit-transaction',
                                        arguments: transaction,
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/finance/add-transaction');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer les transactions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(labelText: 'Type'),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('Tous')),
                DropdownMenuItem(value: 'income', child: Text('Revenus')),
                DropdownMenuItem(value: 'expense', child: Text('Dépenses')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(labelText: 'Catégorie'),
              items: [
                const DropdownMenuItem(value: 'all', child: Text('Toutes')),
                ...ref
                    .read(financeProvider)
                    .transactions
                    .map((t) => t.category)
                    .toSet()
                    .map((category) => DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedType = 'all';
                _selectedCategory = 'all';
              });
              Navigator.pop(context);
            },
            child: const Text('Réinitialiser'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}