const checkRole = (...allowedRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Non authentifié'
        });
      }

      const hasRole = allowedRoles.includes(req.user.role);
      if (!hasRole) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé pour ce rôle'
        });
      }

      next();
    } catch (error) {
      console.error('Erreur dans checkRole middleware:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification des rôles',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
};

const isEleveur = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Non authentifié'
      });
    }

    if (req.user.role !== 'eleveur') {
      return res.status(403).json({
        success: false,
        message: 'Accès réservé aux éleveurs'
      });
    }

    // Vérifier si l'éleveur accède à ses propres ressources
    const requestedEleveurId = parseInt(req.params.id);
    if (requestedEleveurId && req.user.profile_id !== requestedEleveurId) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé aux ressources d\'un autre éleveur'
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans isEleveur middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification du rôle éleveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const isOuvrier = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Non authentifié'
      });
    }

    if (req.user.role !== 'ouvrier') {
      return res.status(403).json({
        success: false,
        message: 'Accès réservé aux ouvriers'
      });
    }

    // Vérifier si l'ouvrier accède à ses propres ressources
    const requestedOuvrierId = parseInt(req.params.id);
    if (requestedOuvrierId && req.user.profile_id !== requestedOuvrierId) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé aux ressources d\'un autre ouvrier'
      });
    }

    next();
  } catch (error) {
    console.error('Erreur dans isOuvrier middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification du rôle ouvrier',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  checkRole,
  isEleveur,
  isOuvrier
};