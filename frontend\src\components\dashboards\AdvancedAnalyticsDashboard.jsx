import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  PredictiveText as PredictiveIcon,
  Lightbulb as LightbulbIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

const AdvancedAnalyticsDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('30days');
  const [tabValue, setTabValue] = useState(0);
  const [analyticsData, setAnalyticsData] = useState(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [period]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/integrations/analytics/comprehensive?period=${period}`);
      setAnalyticsData(response.data.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des analytics');
      console.error('Erreur analytics:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toFixed(0) || '0';
  };

  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'production': return <AssessmentIcon />;
      case 'financial': return <TrendingUpIcon />;
      case 'iot': return <WarningIcon />;
      default: return <InfoIcon />;
    }
  };

  const getRecommendationColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  if (!analyticsData) {
    return (
      <Alert severity="info" sx={{ mb: 3 }}>
        Aucune donnée d'analytics disponible
      </Alert>
    );
  }

  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

  return (
    <Box>
      {/* Header avec contrôles */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" gutterBottom>
          Analytics Avancées
        </Typography>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Période</InputLabel>
          <Select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
          >
            <MenuItem value="7days">7 jours</MenuItem>
            <MenuItem value="30days">30 jours</MenuItem>
            <MenuItem value="90days">90 jours</MenuItem>
            <MenuItem value="1year">1 an</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* KPIs principaux */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AssessmentIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Efficacité Production
                  </Typography>
                  <Typography variant="h5">
                    {analyticsData.performance_kpis?.production_efficiency || 0}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Performance Financière
                  </Typography>
                  <Typography variant="h5">
                    {analyticsData.performance_kpis?.financial_performance || 0}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TimelineIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Taux de Ponte
                  </Typography>
                  <Typography variant="h5">
                    {analyticsData.performance_kpis?.operational_kpis?.egg_production_rate || 0}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PredictiveIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Prédiction Mois Prochain
                  </Typography>
                  <Typography variant="h5">
                    {formatNumber(analyticsData.predictive?.next_month_production || 0)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Onglets pour les différentes vues */}
      <Paper elevation={2} sx={{ mb: 4 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)} variant="scrollable" scrollButtons="auto">
          <Tab label="Vue d'ensemble" />
          <Tab label="Production" />
          <Tab label="Financier" />
          <Tab label="IoT & Environnement" />
          <Tab label="Prédictions" />
          <Tab label="Recommandations" />
        </Tabs>
      </Paper>

      {/* Tab 0: Vue d'ensemble */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Résumé de Production
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Œufs produits (période)
                  </Typography>
                  <Typography variant="h4">
                    {formatNumber(analyticsData.production?.summary?.total_eggs_period || 0)}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Taux de mortalité moyen
                  </Typography>
                  <Typography variant="h6" color={analyticsData.production?.summary?.avg_mortality_rate > 5 ? 'error' : 'success'}>
                    {(analyticsData.production?.summary?.avg_mortality_rate || 0).toFixed(1)}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Résumé Financier
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Chiffre d'affaires
                  </Typography>
                  <Typography variant="h4">
                    {formatNumber(analyticsData.financial?.summary?.total_revenue || 0)} DA
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Marge bénéficiaire
                  </Typography>
                  <Typography variant="h6" color={analyticsData.financial?.summary?.profit_margin > 15 ? 'success' : 'warning'}>
                    {(analyticsData.financial?.summary?.profit_margin || 0).toFixed(1)}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 1: Production */}
      {tabValue === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Production d'Œufs Quotidienne
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={analyticsData.production?.egg_production || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="total_eggs" stroke="#8884d8" name="Œufs produits" />
                    <Line type="monotone" dataKey="broken_eggs" stroke="#ff7300" name="Œufs cassés" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Consommation d'Aliment
                </Typography>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={analyticsData.production?.feed_consumption || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="total_feed" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  KPIs Opérationnels
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">Ratio de Conversion Alimentaire</Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={(analyticsData.performance_kpis?.operational_kpis?.feed_conversion_ratio || 0) * 20} 
                    sx={{ mb: 1 }}
                  />
                  <Typography variant="caption">
                    {(analyticsData.performance_kpis?.operational_kpis?.feed_conversion_ratio || 0).toFixed(2)}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">Taux de Production</Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={analyticsData.performance_kpis?.operational_kpis?.egg_production_rate || 0} 
                    sx={{ mb: 1 }}
                  />
                  <Typography variant="caption">
                    {(analyticsData.performance_kpis?.operational_kpis?.egg_production_rate || 0).toFixed(1)}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 2: Financier */}
      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Évolution du Chiffre d'Affaires
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={analyticsData.financial?.revenue_data || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="daily_revenue" stroke="#8884d8" fill="#8884d8" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Répartition des Coûts
                </Typography>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Alimentation', value: 60 },
                        { name: 'Vétérinaire', value: 20 },
                        { name: 'Autres', value: 20 }
                      ]}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                    >
                      {[60, 20, 20].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Indicateurs Financiers
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Revenus Totaux
                  </Typography>
                  <Typography variant="h5">
                    {formatNumber(analyticsData.financial?.summary?.total_revenue || 0)} DA
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Coûts Totaux
                  </Typography>
                  <Typography variant="h5">
                    {formatNumber(analyticsData.financial?.summary?.total_costs || 0)} DA
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary">
                    Bénéfice Net
                  </Typography>
                  <Typography variant="h5" color={analyticsData.financial?.summary?.net_profit > 0 ? 'success.main' : 'error.main'}>
                    {formatNumber(analyticsData.financial?.summary?.net_profit || 0)} DA
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tab 5: Recommandations */}
      {tabValue === 5 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recommandations Intelligentes
                </Typography>
                <List>
                  {(analyticsData.recommendations || []).map((recommendation, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {getRecommendationIcon(recommendation.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="subtitle1">
                              {recommendation.title}
                            </Typography>
                            <Chip 
                              label={recommendation.priority} 
                              color={getRecommendationColor(recommendation.priority)}
                              size="small"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              {recommendation.description}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              Actions recommandées: {recommendation.actions?.join(', ')}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default AdvancedAnalyticsDashboard;
