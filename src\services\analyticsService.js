const { QueryTypes } = require('sequelize');
const sequelize = require('../config/database');
const weatherService = require('./weatherService');
const iotService = require('./iotService');

/**
 * Service d'analytics avancées
 * Combine les données de production, météo, IoT et ventes pour des insights complets
 */
class AnalyticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Obtenir un dashboard analytics complet pour un éleveur
   */
  async getComprehensiveAnalytics(eleveurId, period = '30days') {
    const cacheKey = `analytics_${eleveurId}_${period}`;
    
    // Vérifier le cache
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const [
        productionMetrics,
        financialMetrics,
        weatherImpact,
        iotInsights,
        performanceKPIs,
        predictiveAnalytics
      ] = await Promise.all([
        this.getProductionMetrics(eleveurId, period),
        this.getFinancialMetrics(eleveurId, period),
        this.getWeatherImpactAnalysis(eleveurId, period),
        this.getIoTInsights(eleveurId, period),
        this.getPerformanceKPIs(eleveurId, period),
        this.getPredictiveAnalytics(eleveurId)
      ]);

      const analytics = {
        period,
        generated_at: new Date().toISOString(),
        production: productionMetrics,
        financial: financialMetrics,
        weather_impact: weatherImpact,
        iot_insights: iotInsights,
        performance_kpis: performanceKPIs,
        predictive: predictiveAnalytics,
        recommendations: this.generateRecommendations({
          productionMetrics,
          financialMetrics,
          weatherImpact,
          iotInsights,
          performanceKPIs
        })
      };

      // Mettre en cache
      this.cache.set(cacheKey, {
        data: analytics,
        timestamp: Date.now()
      });

      return analytics;
    } catch (error) {
      console.error('Erreur lors de la génération des analytics:', error);
      throw error;
    }
  }

  /**
   * Métriques de production
   */
  async getProductionMetrics(eleveurId, period) {
    const dateFilter = this.getDateFilter(period);
    
    try {
      const [eggProduction, mortalityRates, feedConsumption] = await Promise.all([
        // Production d'œufs
        sequelize.query(`
          SELECT 
            DATE(date_production) as date,
            SUM(quantite_collectee) as total_eggs,
            AVG(quantite_collectee) as avg_daily_eggs,
            SUM(oeufs_casses) as broken_eggs,
            SUM(oeufs_defectueux) as defective_eggs
          FROM production_oeufs 
          WHERE eleveur_id = $1 ${dateFilter}
          GROUP BY DATE(date_production)
          ORDER BY date DESC
        `, {
          bind: [eleveurId],
          type: QueryTypes.SELECT
        }),

        // Taux de mortalité
        sequelize.query(`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as total_animals,
            COUNT(CASE WHEN statut = 'mort' THEN 1 END) as deaths,
            (COUNT(CASE WHEN statut = 'mort' THEN 1 END) * 100.0 / COUNT(*)) as mortality_rate
          FROM volailles 
          WHERE eleveur_id = $1 ${dateFilter}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `, {
          bind: [eleveurId],
          type: QueryTypes.SELECT
        }),

        // Consommation d'aliment
        sequelize.query(`
          SELECT 
            DATE(date_distribution) as date,
            SUM(quantite_distribuee) as total_feed,
            AVG(quantite_distribuee) as avg_feed_per_animal,
            type_aliment
          FROM feed_distributions 
          WHERE eleveur_id = $1 ${dateFilter}
          GROUP BY DATE(date_distribution), type_aliment
          ORDER BY date DESC
        `, {
          bind: [eleveurId],
          type: QueryTypes.SELECT
        })
      ]);

      return {
        egg_production: eggProduction,
        mortality_rates: mortalityRates,
        feed_consumption: feedConsumption,
        summary: {
          total_eggs_period: eggProduction.reduce((sum, day) => sum + (day.total_eggs || 0), 0),
          avg_mortality_rate: mortalityRates.length > 0 
            ? mortalityRates.reduce((sum, day) => sum + (day.mortality_rate || 0), 0) / mortalityRates.length 
            : 0,
          total_feed_consumed: feedConsumption.reduce((sum, day) => sum + (day.total_feed || 0), 0)
        }
      };
    } catch (error) {
      console.error('Erreur métriques de production:', error);
      return { egg_production: [], mortality_rates: [], feed_consumption: [], summary: {} };
    }
  }

  /**
   * Métriques financières
   */
  async getFinancialMetrics(eleveurId, period) {
    const dateFilter = this.getDateFilter(period);
    
    try {
      const [revenue, costs, profitability] = await Promise.all([
        // Revenus des ventes
        sequelize.query(`
          SELECT 
            DATE(date_vente) as date,
            SUM(prix_total) as daily_revenue,
            COUNT(*) as sales_count,
            AVG(prix_unitaire) as avg_unit_price
          FROM ventes v
          JOIN volailles vol ON v.volaille_id = vol.id
          WHERE vol.eleveur_id = $1 ${dateFilter}
          GROUP BY DATE(date_vente)
          ORDER BY date DESC
        `, {
          bind: [eleveurId],
          type: QueryTypes.SELECT
        }),

        // Coûts (aliments, vétérinaire, etc.)
        sequelize.query(`
          SELECT 
            DATE(created_at) as date,
            'feed' as cost_type,
            SUM(cout_estime) as amount
          FROM feed_distributions 
          WHERE eleveur_id = $1 ${dateFilter}
          GROUP BY DATE(created_at)
          
          UNION ALL
          
          SELECT 
            DATE(date_consultation) as date,
            'veterinary' as cost_type,
            SUM(cout) as amount
          FROM consultations 
          WHERE eleveur_id = $1 ${dateFilter}
          GROUP BY DATE(date_consultation)
          
          ORDER BY date DESC
        `, {
          bind: [eleveurId],
          type: QueryTypes.SELECT
        }),

        // Analyse de rentabilité
        sequelize.query(`
          SELECT 
            EXTRACT(MONTH FROM date_vente) as month,
            EXTRACT(YEAR FROM date_vente) as year,
            SUM(prix_total) as monthly_revenue
          FROM ventes v
          JOIN volailles vol ON v.volaille_id = vol.id
          WHERE vol.eleveur_id = $1 ${dateFilter}
          GROUP BY EXTRACT(YEAR FROM date_vente), EXTRACT(MONTH FROM date_vente)
          ORDER BY year DESC, month DESC
        `, {
          bind: [eleveurId],
          type: QueryTypes.SELECT
        })
      ]);

      const totalRevenue = revenue.reduce((sum, day) => sum + (day.daily_revenue || 0), 0);
      const totalCosts = costs.reduce((sum, cost) => sum + (cost.amount || 0), 0);

      return {
        revenue_data: revenue,
        cost_data: costs,
        profitability_data: profitability,
        summary: {
          total_revenue: totalRevenue,
          total_costs: totalCosts,
          net_profit: totalRevenue - totalCosts,
          profit_margin: totalRevenue > 0 ? ((totalRevenue - totalCosts) / totalRevenue) * 100 : 0
        }
      };
    } catch (error) {
      console.error('Erreur métriques financières:', error);
      return { revenue_data: [], cost_data: [], profitability_data: [], summary: {} };
    }
  }

  /**
   * Analyse de l'impact météorologique
   */
  async getWeatherImpactAnalysis(eleveurId, period) {
    try {
      // Obtenir les données météo historiques (simulées)
      const weatherHistory = await this.getHistoricalWeatherData(period);
      
      // Corréler avec la production d'œufs
      const productionCorrelation = await this.correlateWeatherWithProduction(eleveurId, weatherHistory);

      return {
        weather_history: weatherHistory,
        production_correlation: productionCorrelation,
        insights: {
          optimal_temperature_range: { min: 18, max: 24 },
          optimal_humidity_range: { min: 50, max: 70 },
          weather_impact_score: this.calculateWeatherImpactScore(productionCorrelation)
        }
      };
    } catch (error) {
      console.error('Erreur analyse météo:', error);
      return { weather_history: [], production_correlation: [], insights: {} };
    }
  }

  /**
   * Insights IoT
   */
  async getIoTInsights(eleveurId, period) {
    try {
      const devices = iotService.getDevices();
      const alerts = iotService.getAlerts();
      
      // Analyser les tendances des capteurs
      const sensorTrends = {};
      devices.forEach(device => {
        const sensorData = iotService.getSensorData(device.id, 100);
        sensorTrends[device.id] = this.analyzeSensorTrends(sensorData);
      });

      return {
        device_status: devices,
        sensor_trends: sensorTrends,
        alert_summary: {
          total_alerts: alerts.length,
          unacknowledged_alerts: alerts.filter(a => !a.acknowledged).length,
          critical_alerts: alerts.filter(a => a.severity === 'high').length
        },
        environmental_conditions: this.getEnvironmentalSummary(sensorTrends)
      };
    } catch (error) {
      console.error('Erreur insights IoT:', error);
      return { device_status: [], sensor_trends: {}, alert_summary: {}, environmental_conditions: {} };
    }
  }

  /**
   * KPIs de performance
   */
  async getPerformanceKPIs(eleveurId, period) {
    try {
      const productionMetrics = await this.getProductionMetrics(eleveurId, period);
      const financialMetrics = await this.getFinancialMetrics(eleveurId, period);

      return {
        production_efficiency: this.calculateProductionEfficiency(productionMetrics),
        financial_performance: this.calculateFinancialPerformance(financialMetrics),
        operational_kpis: {
          feed_conversion_ratio: this.calculateFeedConversionRatio(productionMetrics),
          egg_production_rate: this.calculateEggProductionRate(productionMetrics),
          mortality_rate: productionMetrics.summary.avg_mortality_rate || 0,
          profitability_index: financialMetrics.summary.profit_margin || 0
        }
      };
    } catch (error) {
      console.error('Erreur KPIs de performance:', error);
      return { production_efficiency: 0, financial_performance: 0, operational_kpis: {} };
    }
  }

  /**
   * Analytics prédictives
   */
  async getPredictiveAnalytics(eleveurId) {
    try {
      // Prédictions basées sur les tendances historiques
      const predictions = {
        next_month_production: await this.predictNextMonthProduction(eleveurId),
        seasonal_trends: await this.analyzeSeasonalTrends(eleveurId),
        risk_assessment: await this.assessRisks(eleveurId),
        optimization_opportunities: await this.identifyOptimizationOpportunities(eleveurId)
      };

      return predictions;
    } catch (error) {
      console.error('Erreur analytics prédictives:', error);
      return { next_month_production: 0, seasonal_trends: [], risk_assessment: {}, optimization_opportunities: [] };
    }
  }

  /**
   * Générer des recommandations basées sur toutes les données
   */
  generateRecommendations(analyticsData) {
    const recommendations = [];

    // Recommandations basées sur la production
    if (analyticsData.productionMetrics.summary.avg_mortality_rate > 5) {
      recommendations.push({
        type: 'production',
        priority: 'high',
        title: 'Taux de mortalité élevé',
        description: 'Le taux de mortalité dépasse 5%. Consultez un vétérinaire.',
        actions: ['Consultation vétérinaire', 'Vérification des conditions d\'élevage', 'Analyse de l\'alimentation']
      });
    }

    // Recommandations financières
    if (analyticsData.financialMetrics.summary.profit_margin < 10) {
      recommendations.push({
        type: 'financial',
        priority: 'medium',
        title: 'Marge bénéficiaire faible',
        description: 'La marge bénéficiaire est inférieure à 10%.',
        actions: ['Optimiser les coûts d\'alimentation', 'Améliorer les prix de vente', 'Réduire les pertes']
      });
    }

    // Recommandations IoT
    if (analyticsData.iotInsights.alert_summary.critical_alerts > 0) {
      recommendations.push({
        type: 'iot',
        priority: 'high',
        title: 'Alertes critiques IoT',
        description: 'Des alertes critiques nécessitent une attention immédiate.',
        actions: ['Vérifier les capteurs', 'Ajuster les conditions environnementales', 'Maintenance préventive']
      });
    }

    return recommendations;
  }

  /**
   * Utilitaires
   */
  getDateFilter(period) {
    switch (period) {
      case '7days':
        return "AND created_at >= NOW() - INTERVAL '7 days'";
      case '30days':
        return "AND created_at >= NOW() - INTERVAL '30 days'";
      case '90days':
        return "AND created_at >= NOW() - INTERVAL '90 days'";
      case '1year':
        return "AND created_at >= NOW() - INTERVAL '1 year'";
      default:
        return "AND created_at >= NOW() - INTERVAL '30 days'";
    }
  }

  async getHistoricalWeatherData(period) {
    // Simuler des données météo historiques
    const days = period === '7days' ? 7 : period === '30days' ? 30 : 90;
    const weatherData = [];
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      weatherData.push({
        date: date.toISOString().split('T')[0],
        temperature: 20 + Math.random() * 10,
        humidity: 50 + Math.random() * 30,
        precipitation: Math.random() * 5
      });
    }
    
    return weatherData.reverse();
  }

  calculateWeatherImpactScore(correlationData) {
    // Calculer un score d'impact météorologique (0-100)
    return Math.round(75 + Math.random() * 25); // Simulé
  }

  analyzeSensorTrends(sensorData) {
    if (!sensorData || sensorData.length === 0) return { trend: 'stable', change: 0 };
    
    const recent = sensorData.slice(-10);
    const older = sensorData.slice(-20, -10);
    
    if (recent.length === 0 || older.length === 0) return { trend: 'stable', change: 0 };
    
    const recentAvg = recent.reduce((sum, d) => sum + (d.temperature || d.humidity || d.feed_level || 0), 0) / recent.length;
    const olderAvg = older.reduce((sum, d) => sum + (d.temperature || d.humidity || d.feed_level || 0), 0) / older.length;
    
    const change = ((recentAvg - olderAvg) / olderAvg) * 100;
    
    return {
      trend: change > 5 ? 'increasing' : change < -5 ? 'decreasing' : 'stable',
      change: Math.round(change * 100) / 100
    };
  }

  getEnvironmentalSummary(sensorTrends) {
    return {
      temperature_status: 'optimal',
      humidity_status: 'optimal',
      overall_conditions: 'good'
    };
  }

  calculateProductionEfficiency(productionMetrics) {
    // Calculer l'efficacité de production (0-100)
    return Math.round(80 + Math.random() * 20); // Simulé
  }

  calculateFinancialPerformance(financialMetrics) {
    // Calculer la performance financière (0-100)
    return Math.round(70 + Math.random() * 30); // Simulé
  }

  calculateFeedConversionRatio(productionMetrics) {
    // Ratio de conversion alimentaire
    return 2.1 + Math.random() * 0.5; // Simulé
  }

  calculateEggProductionRate(productionMetrics) {
    // Taux de production d'œufs (%)
    return Math.round(75 + Math.random() * 20); // Simulé
  }

  async predictNextMonthProduction(eleveurId) {
    // Prédiction simple basée sur la tendance
    return Math.round(1000 + Math.random() * 500); // Simulé
  }

  async analyzeSeasonalTrends(eleveurId) {
    // Analyser les tendances saisonnières
    return [
      { season: 'spring', production_factor: 1.1 },
      { season: 'summer', production_factor: 0.9 },
      { season: 'autumn', production_factor: 1.0 },
      { season: 'winter', production_factor: 0.8 }
    ];
  }

  async assessRisks(eleveurId) {
    return {
      disease_risk: 'low',
      market_risk: 'medium',
      weather_risk: 'low',
      overall_risk_score: 25
    };
  }

  async identifyOptimizationOpportunities(eleveurId) {
    return [
      {
        area: 'feed_efficiency',
        potential_savings: '15%',
        description: 'Optimisation de la distribution d\'aliments'
      },
      {
        area: 'production_timing',
        potential_improvement: '8%',
        description: 'Ajustement des cycles de production'
      }
    ];
  }

  async correlateWeatherWithProduction(eleveurId, weatherHistory) {
    // Corréler les données météo avec la production
    return weatherHistory.map(day => ({
      ...day,
      production_impact: Math.random() > 0.5 ? 'positive' : 'neutral'
    }));
  }
}

module.exports = new AnalyticsService();
