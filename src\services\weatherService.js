const axios = require('axios');

/**
 * Service de gestion des données météorologiques
 * Intègre plusieurs APIs météo pour fournir des données complètes
 */
class WeatherService {
  constructor() {
    // Configuration des APIs météo
    this.openWeatherMapApiKey = process.env.OPENWEATHERMAP_API_KEY;
    this.weatherApiKey = process.env.WEATHER_API_KEY;
    
    // URLs de base des APIs
    this.openWeatherMapBaseUrl = 'https://api.openweathermap.org/data/2.5';
    this.weatherApiBaseUrl = 'https://api.weatherapi.com/v1';
    
    // Configuration par défaut
    this.defaultLocation = {
      lat: 36.7538,
      lon: 3.0588,
      name: 'Alger, Algérie'
    };
  }

  /**
   * Obtenir les données météorologiques actuelles
   * @param {Object} location - Coordonnées ou nom de la localisation
   * @returns {Object} Données météo actuelles avec recommandations agricoles
   */
  async getCurrentWeather(location = this.defaultLocation) {
    try {
      let weatherData;
      
      // Essayer d'abord OpenWeatherMap si la clé API est disponible
      if (this.openWeatherMapApiKey) {
        weatherData = await this.getOpenWeatherMapCurrent(location);
      } else if (this.weatherApiKey) {
        weatherData = await this.getWeatherApiCurrent(location);
      } else {
        // Utiliser des données simulées si aucune API n'est configurée
        weatherData = this.getMockCurrentWeather(location);
      }

      // Ajouter les recommandations agricoles
      weatherData.agricultural_insights = this.generateAgriculturalInsights(weatherData);
      
      return weatherData;
    } catch (error) {
      console.error('Erreur lors de la récupération des données météo:', error);
      // Retourner des données simulées en cas d'erreur
      return this.getMockCurrentWeather(location);
    }
  }

  /**
   * Obtenir les prévisions météorologiques
   * @param {Object} location - Coordonnées ou nom de la localisation
   * @param {number} days - Nombre de jours de prévision (1-7)
   * @returns {Array} Prévisions météo avec recommandations
   */
  async getForecast(location = this.defaultLocation, days = 5) {
    try {
      let forecastData;
      
      if (this.openWeatherMapApiKey) {
        forecastData = await this.getOpenWeatherMapForecast(location, days);
      } else if (this.weatherApiKey) {
        forecastData = await this.getWeatherApiForecast(location, days);
      } else {
        forecastData = this.getMockForecast(location, days);
      }

      // Ajouter les recommandations pour chaque jour
      forecastData.forEach(day => {
        day.agricultural_recommendations = this.generateDailyRecommendations(day);
      });

      return forecastData;
    } catch (error) {
      console.error('Erreur lors de la récupération des prévisions:', error);
      return this.getMockForecast(location, days);
    }
  }

  /**
   * Récupérer les données actuelles depuis OpenWeatherMap
   */
  async getOpenWeatherMapCurrent(location) {
    const url = `${this.openWeatherMapBaseUrl}/weather`;
    const params = {
      appid: this.openWeatherMapApiKey,
      units: 'metric',
      lang: 'fr'
    };

    if (location.lat && location.lon) {
      params.lat = location.lat;
      params.lon = location.lon;
    } else {
      params.q = location.name || location;
    }

    const response = await axios.get(url, { params });
    const data = response.data;

    return {
      location: data.name + ', ' + data.sys.country,
      temperature: Math.round(data.main.temp),
      feels_like: Math.round(data.main.feels_like),
      humidity: data.main.humidity,
      pressure: data.main.pressure,
      wind_speed: data.wind.speed,
      wind_direction: this.getWindDirection(data.wind.deg),
      visibility: data.visibility / 1000, // Convert to km
      description: data.weather[0].description,
      icon: data.weather[0].icon,
      timestamp: new Date().toISOString(),
      source: 'OpenWeatherMap'
    };
  }

  /**
   * Récupérer les prévisions depuis OpenWeatherMap
   */
  async getOpenWeatherMapForecast(location, days) {
    const url = `${this.openWeatherMapBaseUrl}/forecast`;
    const params = {
      appid: this.openWeatherMapApiKey,
      units: 'metric',
      lang: 'fr'
    };

    if (location.lat && location.lon) {
      params.lat = location.lat;
      params.lon = location.lon;
    } else {
      params.q = location.name || location;
    }

    const response = await axios.get(url, { params });
    const data = response.data;

    // Grouper les prévisions par jour
    const dailyForecasts = {};
    data.list.forEach(item => {
      const date = item.dt_txt.split(' ')[0];
      if (!dailyForecasts[date]) {
        dailyForecasts[date] = [];
      }
      dailyForecasts[date].push(item);
    });

    // Créer les prévisions quotidiennes
    const forecast = Object.keys(dailyForecasts)
      .slice(0, days)
      .map(date => {
        const dayData = dailyForecasts[date];
        const temps = dayData.map(d => d.main.temp);
        const humidity = dayData.map(d => d.main.humidity);
        
        return {
          date,
          temperature_min: Math.round(Math.min(...temps)),
          temperature_max: Math.round(Math.max(...temps)),
          humidity: Math.round(humidity.reduce((a, b) => a + b) / humidity.length),
          precipitation: dayData.reduce((sum, d) => sum + (d.rain?.['3h'] || 0), 0),
          wind_speed: Math.round(dayData[0].wind.speed),
          description: dayData[0].weather[0].description,
          icon: dayData[0].weather[0].icon
        };
      });

    return forecast;
  }

  /**
   * Générer des données météo simulées
   */
  getMockCurrentWeather(location) {
    const baseTemp = 20 + Math.random() * 15; // 20-35°C
    return {
      location: location.name || 'Alger, Algérie',
      temperature: Math.round(baseTemp),
      feels_like: Math.round(baseTemp + (Math.random() - 0.5) * 4),
      humidity: Math.round(50 + Math.random() * 30),
      pressure: Math.round(1010 + Math.random() * 20),
      wind_speed: Math.round(Math.random() * 15),
      wind_direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
      visibility: Math.round(8 + Math.random() * 7),
      description: ['Ensoleillé', 'Partiellement nuageux', 'Nuageux', 'Légèrement pluvieux'][Math.floor(Math.random() * 4)],
      icon: '01d',
      timestamp: new Date().toISOString(),
      source: 'Simulation'
    };
  }

  /**
   * Générer des prévisions simulées
   */
  getMockForecast(location, days) {
    const forecast = [];
    const baseTemp = 20 + Math.random() * 10;
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      const tempVariation = (Math.random() - 0.5) * 8;
      forecast.push({
        date: date.toISOString().split('T')[0],
        temperature_min: Math.round(baseTemp + tempVariation - 3),
        temperature_max: Math.round(baseTemp + tempVariation + 5),
        humidity: Math.round(50 + Math.random() * 30),
        precipitation: Math.random() * 10,
        wind_speed: Math.round(5 + Math.random() * 10),
        description: ['Ensoleillé', 'Nuageux', 'Pluvieux', 'Partiellement nuageux'][Math.floor(Math.random() * 4)],
        icon: '01d'
      });
    }
    
    return forecast;
  }

  /**
   * Générer des insights agricoles basés sur les conditions météo
   */
  generateAgriculturalInsights(weatherData) {
    const insights = {
      poultry_comfort: 'optimal',
      ventilation_needed: false,
      heating_needed: false,
      cooling_needed: false,
      recommendations: []
    };

    // Analyse de la température
    if (weatherData.temperature < 15) {
      insights.poultry_comfort = 'froid';
      insights.heating_needed = true;
      insights.recommendations.push('Activer le chauffage dans les poulaillers');
      insights.recommendations.push('Vérifier l\'isolation des bâtiments');
    } else if (weatherData.temperature > 30) {
      insights.poultry_comfort = 'chaud';
      insights.cooling_needed = true;
      insights.ventilation_needed = true;
      insights.recommendations.push('Augmenter la ventilation');
      insights.recommendations.push('Fournir de l\'ombre supplémentaire');
      insights.recommendations.push('Assurer un accès constant à l\'eau fraîche');
    } else if (weatherData.temperature > 25) {
      insights.ventilation_needed = true;
      insights.recommendations.push('Maintenir une bonne ventilation');
    }

    // Analyse de l'humidité
    if (weatherData.humidity > 80) {
      insights.recommendations.push('Surveiller l\'humidité dans les bâtiments');
      insights.recommendations.push('Renforcer la ventilation pour réduire l\'humidité');
    } else if (weatherData.humidity < 40) {
      insights.recommendations.push('Surveiller la déshydratation des animaux');
    }

    // Analyse du vent
    if (weatherData.wind_speed > 20) {
      insights.recommendations.push('Protéger les animaux des vents forts');
      insights.recommendations.push('Vérifier la solidité des structures');
    }

    return insights;
  }

  /**
   * Générer des recommandations quotidiennes
   */
  generateDailyRecommendations(dayData) {
    const recommendations = [];

    if (dayData.precipitation > 5) {
      recommendations.push('Prévoir un abri supplémentaire contre la pluie');
      recommendations.push('Vérifier l\'étanchéité des toitures');
    }

    if (dayData.temperature_max > 30) {
      recommendations.push('Planifier des activités tôt le matin');
      recommendations.push('Augmenter la fréquence d\'abreuvement');
    }

    if (dayData.temperature_min < 10) {
      recommendations.push('Prévoir un chauffage d\'appoint');
      recommendations.push('Vérifier l\'isolation nocturne');
    }

    return recommendations;
  }

  /**
   * Convertir les degrés en direction du vent
   */
  getWindDirection(degrees) {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  }

  /**
   * Obtenir des alertes météorologiques
   */
  async getWeatherAlerts(location = this.defaultLocation) {
    try {
      const currentWeather = await this.getCurrentWeather(location);
      const alerts = [];

      // Alertes basées sur les conditions actuelles
      if (currentWeather.temperature > 35) {
        alerts.push({
          type: 'warning',
          severity: 'high',
          title: 'Température élevée',
          message: 'Risque de stress thermique pour les volailles',
          recommendations: ['Augmenter la ventilation', 'Fournir plus d\'eau fraîche']
        });
      }

      if (currentWeather.temperature < 5) {
        alerts.push({
          type: 'warning',
          severity: 'high',
          title: 'Température très basse',
          message: 'Risque d\'hypothermie pour les jeunes animaux',
          recommendations: ['Activer le chauffage', 'Vérifier l\'isolation']
        });
      }

      if (currentWeather.wind_speed > 25) {
        alerts.push({
          type: 'alert',
          severity: 'medium',
          title: 'Vents forts',
          message: 'Risque de dommages aux structures',
          recommendations: ['Sécuriser les équipements mobiles', 'Vérifier les toitures']
        });
      }

      return alerts;
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes météo:', error);
      return [];
    }
  }
}

module.exports = new WeatherService();
