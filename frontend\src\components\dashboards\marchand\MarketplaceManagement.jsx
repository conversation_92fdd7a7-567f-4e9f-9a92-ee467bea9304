import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Fab,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Store as StoreIcon,
  AttachMoney as MoneyIcon,
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';

const MarketplaceManagement = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    prix_unitaire: '',
    quantite_disponible: '',
    espece: '',
    race: '',
    age_semaines: '',
    localisation: '',
    images: []
  });

  const [stats, setStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    totalRevenue: 0,
    pendingOrders: 0
  });

  useEffect(() => {
    loadProducts();
    loadStats();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockProducts = [
        {
          id: 1,
          nom: 'Poules Pondeuses ISA Brown',
          description: 'Poules pondeuses de race ISA Brown, excellente production d\'œufs',
          prix_unitaire: 25.00,
          quantite_disponible: 50,
          espece: 'poule',
          race: 'ISA Brown',
          age_semaines: 18,
          localisation: 'Alger',
          statut: 'active',
          vues: 125,
          created_at: '2025-01-05'
        },
        {
          id: 2,
          nom: 'Poulets de Chair Ross 308',
          description: 'Poulets de chair de race Ross 308, croissance rapide',
          prix_unitaire: 15.00,
          quantite_disponible: 100,
          espece: 'poulet',
          race: 'Ross 308',
          age_semaines: 6,
          localisation: 'Oran',
          statut: 'active',
          vues: 89,
          created_at: '2025-01-03'
        },
        {
          id: 3,
          nom: 'Dindes Bronze',
          description: 'Dindes de race Bronze, idéales pour l\'élevage traditionnel',
          prix_unitaire: 45.00,
          quantite_disponible: 20,
          espece: 'dinde',
          race: 'Bronze',
          age_semaines: 12,
          localisation: 'Constantine',
          statut: 'vendu',
          vues: 67,
          created_at: '2025-01-01'
        }
      ];
      setProducts(mockProducts);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des produits');
      console.error('Erreur produits:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Mock stats - replace with actual API call
      setStats({
        totalProducts: 15,
        activeProducts: 12,
        totalRevenue: 12500,
        pendingOrders: 3
      });
    } catch (err) {
      console.error('Erreur stats:', err);
    }
  };

  const handleOpenDialog = (product = null) => {
    if (product) {
      setSelectedProduct(product);
      setFormData({
        nom: product.nom,
        description: product.description,
        prix_unitaire: product.prix_unitaire,
        quantite_disponible: product.quantite_disponible,
        espece: product.espece,
        race: product.race,
        age_semaines: product.age_semaines,
        localisation: product.localisation,
        images: product.images || []
      });
    } else {
      setSelectedProduct(null);
      setFormData({
        nom: '',
        description: '',
        prix_unitaire: '',
        quantite_disponible: '',
        espece: '',
        race: '',
        age_semaines: '',
        localisation: '',
        images: []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduct(null);
  };

  const handleSubmit = async () => {
    try {
      if (selectedProduct) {
        // Update product
        console.log('Updating product:', selectedProduct.id, formData);
      } else {
        // Create new product
        console.log('Creating new product:', formData);
      }
      handleCloseDialog();
      loadProducts();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la sauvegarde du produit');
      console.error('Erreur sauvegarde:', err);
    }
  };

  const handleDelete = async (productId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      try {
        console.log('Deleting product:', productId);
        loadProducts();
        loadStats();
      } catch (err) {
        setError('Erreur lors de la suppression du produit');
        console.error('Erreur suppression:', err);
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'vendu': return 'default';
      case 'suspendu': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'active': return 'Actif';
      case 'vendu': return 'Vendu';
      case 'suspendu': return 'Suspendu';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <StoreIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Produits
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalProducts}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <InventoryIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Produits Actifs
                  </Typography>
                  <Typography variant="h5">
                    {stats.activeProducts}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <MoneyIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Chiffre d'Affaires
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalRevenue.toLocaleString()} DA
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Commandes en Attente
                  </Typography>
                  <Typography variant="h5">
                    {stats.pendingOrders}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          Mes Produits
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Nouveau Produit
        </Button>
      </Box>

      {/* Products Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nom</TableCell>
              <TableCell>Espèce</TableCell>
              <TableCell>Prix Unitaire</TableCell>
              <TableCell>Quantité</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Vues</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {products.map((product) => (
              <TableRow key={product.id}>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">
                      {product.nom}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {product.race} - {product.age_semaines} semaines
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>{product.espece}</TableCell>
                <TableCell>{product.prix_unitaire} DA</TableCell>
                <TableCell>{product.quantite_disponible}</TableCell>
                <TableCell>
                  <Chip
                    label={getStatusLabel(product.statut)}
                    color={getStatusColor(product.statut)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{product.vues}</TableCell>
                <TableCell>
                  <Tooltip title="Voir">
                    <IconButton size="small">
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Modifier">
                    <IconButton size="small" onClick={() => handleOpenDialog(product)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Supprimer">
                    <IconButton size="small" onClick={() => handleDelete(product.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Product Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedProduct ? 'Modifier le Produit' : 'Nouveau Produit'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nom du produit"
                value={formData.nom}
                onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                type="number"
                label="Prix unitaire (DA)"
                value={formData.prix_unitaire}
                onChange={(e) => setFormData({ ...formData, prix_unitaire: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                type="number"
                label="Quantité disponible"
                value={formData.quantite_disponible}
                onChange={(e) => setFormData({ ...formData, quantite_disponible: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Espèce</InputLabel>
                <Select
                  value={formData.espece}
                  onChange={(e) => setFormData({ ...formData, espece: e.target.value })}
                >
                  <MenuItem value="poule">Poule</MenuItem>
                  <MenuItem value="poulet">Poulet</MenuItem>
                  <MenuItem value="dinde">Dinde</MenuItem>
                  <MenuItem value="canard">Canard</MenuItem>
                  <MenuItem value="oie">Oie</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Race"
                value={formData.race}
                onChange={(e) => setFormData({ ...formData, race: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                type="number"
                label="Âge (semaines)"
                value={formData.age_semaines}
                onChange={(e) => setFormData({ ...formData, age_semaines: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Localisation"
                value={formData.localisation}
                onChange={(e) => setFormData({ ...formData, localisation: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            Annuler
          </Button>
          <Button onClick={handleSubmit} variant="contained">
            {selectedProduct ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MarketplaceManagement;
