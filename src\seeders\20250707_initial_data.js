'use strict';
const bcrypt = require('bcryptjs');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Créer les rôles de base
    const roles = [
      {
        name: 'admin',
        description: 'Administrateur système',
        permissions: JSON.stringify([
          'manage_users',
          'manage_roles',
          'manage_eleveurs',
          'manage_ouvriers',
          'view_all_data',
          'manage_system'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'eleveur',
        description: 'Éleveur avec accès à ses données',
        permissions: JSON.stringify([
          'manage_own_profile',
          'manage_own_ouvriers',
          'manage_own_volailles',
          'view_own_statistics',
          'manage_own_alerts'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'ouvrier',
        description: 'Ouvrier avec accès limité',
        permissions: JSON.stringify([
          'view_own_profile',
          'update_own_profile',
          'view_assigned_tasks',
          'update_task_status',
          'view_eleveur_data',
          'create_daily_reports'
        ]),
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Insérer les rôles
    await queryInterface.bulkInsert('roles', roles, {
      ignoreDuplicates: true
    });

    // Récupérer l'ID du rôle admin
    const adminRole = await queryInterface.sequelize.query(
      'SELECT id FROM roles WHERE name = \'admin\'',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (adminRole.length > 0) {
      // Créer un utilisateur admin par défaut
      const adminUser = {
        username: 'admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('Admin123!', 10),
        role_id: adminRole[0].id,
        first_name: 'Admin',
        last_name: 'System',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      };

      // Insérer l'utilisateur admin
      await queryInterface.bulkInsert('users', [adminUser], {
        ignoreDuplicates: true
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Supprimer l'utilisateur admin
    await queryInterface.bulkDelete('users', {
      email: '<EMAIL>'
    });

    // Supprimer tous les rôles
    await queryInterface.bulkDelete('roles', null, {});
  }
};