/**
 * Composant de statistiques détaillées pour les éleveurs
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Alert,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  PieChart as PieChartIcon,
  Timeline as TimelineIcon,
  Pets as PetsIcon,
  AttachMoney as MoneyIcon,
  Egg as EggIcon,
  LocalHospital as HealthIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import { eleveurAPI } from '../../services/api';

const EleveurStatistics = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [tabValue, setTabValue] = useState(0);

  const [statistics, setStatistics] = useState({
    production: [],
    ventes: [],
    sante: [],
    financier: [],
    repartition: []
  });

  const [kpis, setKpis] = useState({
    totalVolailles: 0,
    productionMoyenne: 0,
    tauxMortalite: 0,
    chiffreAffaires: 0,
    beneficeNet: 0,
    croissanceMensuelle: 0
  });

  useEffect(() => {
    loadStatistics();
  }, [selectedPeriod]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const [statsResponse, kpisResponse] = await Promise.all([
        eleveurAPI.getDetailedStatistics(user.id, selectedPeriod),
        eleveurAPI.getKPIs(user.id, selectedPeriod)
      ]);

      setStatistics(statsResponse.data || statistics);
      setKpis(kpisResponse.data || kpis);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des statistiques');
      console.error('Erreur statistiques:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const StatCard = ({ title, value, subtitle, icon, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: `${color}.light`,
            color: `${color}.contrastText`,
            mr: 2
          }}>
            {icon}
          </Box>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
        </Box>
        <Typography variant="h4" component="div" gutterBottom>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container>
        <Typography>Chargement des statistiques...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Statistiques Détaillées
        </Typography>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Période</InputLabel>
          <Select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            <MenuItem value="1month">1 Mois</MenuItem>
            <MenuItem value="3months">3 Mois</MenuItem>
            <MenuItem value="6months">6 Mois</MenuItem>
            <MenuItem value="1year">1 Année</MenuItem>
            <MenuItem value="2years">2 Années</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* KPIs principaux */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Total Volailles"
            value={kpis.totalVolailles}
            icon={<PetsIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Production Moy."
            value={`${kpis.productionMoyenne}/j`}
            subtitle="Œufs par jour"
            icon={<EggIcon />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Taux Mortalité"
            value={`${kpis.tauxMortalite}%`}
            icon={<HealthIcon />}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Chiffre d'Affaires"
            value={`${kpis.chiffreAffaires.toLocaleString()} DA`}
            icon={<MoneyIcon />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Bénéfice Net"
            value={`${kpis.beneficeNet.toLocaleString()} DA`}
            icon={<TrendingUpIcon />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard
            title="Croissance"
            value={`${kpis.croissanceMensuelle > 0 ? '+' : ''}${kpis.croissanceMensuelle}%`}
            subtitle="Ce mois"
            icon={<TimelineIcon />}
            color={kpis.croissanceMensuelle > 0 ? 'success' : 'error'}
          />
        </Grid>
      </Grid>

      {/* Onglets pour différentes vues */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Production" />
          <Tab label="Ventes" />
          <Tab label="Santé" />
          <Tab label="Financier" />
        </Tabs>
      </Paper>

      {/* Contenu des onglets */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {/* Évolution de la production */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Évolution de la Production d'Œufs
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={statistics.production}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="oeufs_produits"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.3}
                      name="Œufs Produits"
                    />
                    <Area
                      type="monotone"
                      dataKey="oeufs_vendus"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      fillOpacity={0.3}
                      name="Œufs Vendus"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Répartition par type de volaille */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Répartition par Type
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statistics.repartition}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statistics.repartition.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          {/* Évolution des ventes */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Évolution des Ventes et Revenus
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={statistics.ventes}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="quantite_vendue" fill="#8884d8" name="Quantité Vendue" />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="revenus"
                      stroke="#ff7300"
                      strokeWidth={3}
                      name="Revenus (DA)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 2 && (
        <Grid container spacing={3}>
          {/* Indicateurs de santé */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Taux de Mortalité
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={statistics.sante}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="taux_mortalite"
                      stroke="#ff4444"
                      strokeWidth={2}
                      name="Taux de Mortalité (%)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Interventions Vétérinaires
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={statistics.sante}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="interventions_vet" fill="#00C49F" name="Interventions" />
                    <Bar dataKey="vaccinations" fill="#FFBB28" name="Vaccinations" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 3 && (
        <Grid container spacing={3}>
          {/* Analyse financière */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Analyse Financière - Revenus vs Dépenses
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={statistics.financier}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="revenus"
                      stackId="1"
                      stroke="#00C49F"
                      fill="#00C49F"
                      name="Revenus"
                    />
                    <Area
                      type="monotone"
                      dataKey="depenses"
                      stackId="2"
                      stroke="#FF8042"
                      fill="#FF8042"
                      name="Dépenses"
                    />
                    <Line
                      type="monotone"
                      dataKey="benefice"
                      stroke="#8884d8"
                      strokeWidth={3}
                      name="Bénéfice Net"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default EleveurStatistics;
