const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { checkRole, isEleveur } = require('../middleware/checkRole');
const OuvrierService = require('../services/ouvrierService');
const AuthService = require('../services/authService');

// POST /api/ouvriers - Créer un nouvel ouvrier avec compte utilisateur
router.post('/', auth, checkRole(['admin', 'eleveur']), async (req, res) => {
  try {
    const eleveurId = req.user.role === 'eleveur' ? req.user.profile_id : req.body.eleveur_id;
    
    // Créer l'ouvrier avec son compte utilisateur
    const { user, ouvrier } = await AuthService.createOuvrierWithAccount({
      ...req.body,
      eleveur_id: eleveurId
    }, eleveurId);

    res.status(201).json({
      message: 'Ouvrier créé avec succès',
      ouvrier,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: 'ouvrier'
      }
    });
  } catch (error) {
    console.error('Erreur lors de la création de l\'ouvrier:', error);
    if (error.message.includes('déjà utilisé')) {
      return res.status(400).json({ message: error.message });
    }
    res.status(500).json({ message: 'Erreur lors de la création de l\'ouvrier' });
  }
});

// GET /api/ouvriers - Récupérer tous les ouvriers (admin et éleveur)
router.get('/', auth, checkRole(['admin', 'eleveur']), async (req, res) => {
  try {
    const filters = {
      search: req.query.search,
      statut: req.query.statut
    };

    // Si c'est un éleveur, filtrer uniquement ses ouvriers
    if (req.user.role === 'eleveur') {
      filters.eleveur_id = req.user.profile_id;
    }

    const ouvriers = await OuvrierService.getAllOuvriers(filters);

    res.json({
      success: true,
      data: ouvriers
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des ouvriers:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des ouvriers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/ouvriers/:id - Récupérer un ouvrier par son ID
router.get('/:id', auth, checkRole(['admin', 'eleveur']), async (req, res) => {
  try {
    const ouvrier = await OuvrierService.getOuvrierById(req.params.id);

    // Vérifier si l'éleveur a accès à cet ouvrier
    if (req.user.role === 'eleveur' && ouvrier.eleveur_id !== req.user.profile_id) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé à cet ouvrier'
      });
    }

    res.json({
      success: true,
      data: ouvrier
    });
  } catch (error) {
    if (error.message === 'Ouvrier non trouvé') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    console.error('Erreur lors de la récupération de l\'ouvrier:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'ouvrier',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;