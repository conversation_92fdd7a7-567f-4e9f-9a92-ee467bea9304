import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction_model.dart';
import 'api_service.dart';

class FinanceService {
  final ApiService _apiService;

  FinanceService(this._apiService);

  Future<List<Transaction>> getTransactions(int farmId) async {
    try {
      final response = await _apiService.get('/finance/transactions/$farmId');
      return (response['data'] as List)
          .map((json) => Transaction.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load transactions: ${e.toString()}');
    }
  }

  Future<double> getBalance(int farmId) async {
    try {
      final response = await _apiService.get('/finance/balance/$farmId');
      return (response['balance'] as num).toDouble();
    } catch (e) {
      throw Exception('Failed to load balance: ${e.toString()}');
    }
  }

  Future<void> addTransaction(Transaction transaction) async {
    try {
      await _apiService.post(
        '/finance/transactions',
        transaction.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to add transaction: ${e.toString()}');
    }
  }

  Future<void> updateTransaction(Transaction transaction) async {
    try {
      await _apiService.put(
        '/finance/transactions/${transaction.id}',
        transaction.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to update transaction: ${e.toString()}');
    }
  }

  Future<void> deleteTransaction(int transactionId) async {
    try {
      await _apiService.delete('/finance/transactions/$transactionId');
    } catch (e) {
      throw Exception('Failed to delete transaction: ${e.toString()}');
    }
  }

  Future<Map<String, double>> getBudgetAllocations(int farmId) async {
    try {
      final response = await _apiService.get('/finance/budget/$farmId');
      return Map<String, double>.from(response['allocations']);
    } catch (e) {
      throw Exception('Failed to load budget allocations: ${e.toString()}');
    }
  }

  Future<void> updateBudgetAllocation(
    String category,
    double amount,
    int farmId,
  ) async {
    try {
      await _apiService.put('/finance/budget/$farmId', {
        'category': category,
        'amount': amount,
      });
    } catch (e) {
      throw Exception('Failed to update budget allocation: ${e.toString()}');
    }
  }

  Future<Map<String, dynamic>> getFinancialReports(int farmId) async {
    try {
      final response = await _apiService.get('/finance/reports/$farmId');
      return response['data'] as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to load financial reports: ${e.toString()}');
    }
  }
}

final financeServiceProvider = Provider<FinanceService>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return FinanceService(apiService);
});