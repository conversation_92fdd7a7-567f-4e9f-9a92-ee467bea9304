import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  LocalShipping as ShippingIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  AttachMoney as MoneyIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';

const OrderManagement = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');

  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    loadOrders();
    loadStats();
  }, [statusFilter]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockOrders = [
        {
          id: 1,
          numero_commande: 'CMD-2025-001',
          client: {
            nom: 'Ahmed Benali',
            telephone: '0555123456',
            email: '<EMAIL>',
            adresse: 'Rue des Oliviers, Alger'
          },
          items: [
            {
              id: 1,
              produit_nom: 'Poules Pondeuses ISA Brown',
              quantite: 10,
              prix_unitaire: 25.00,
              total: 250.00
            },
            {
              id: 2,
              produit_nom: 'Aliment Pondeuses Premium',
              quantite: 5,
              prix_unitaire: 45.00,
              total: 225.00
            }
          ],
          total_commande: 475.00,
          statut: 'en_attente',
          date_commande: '2025-01-08',
          date_livraison_prevue: '2025-01-12',
          mode_paiement: 'especes',
          notes: 'Livraison le matin de préférence'
        },
        {
          id: 2,
          numero_commande: 'CMD-2025-002',
          client: {
            nom: 'Fatima Kaci',
            telephone: '0666789012',
            email: '<EMAIL>',
            adresse: 'Avenue de la Liberté, Oran'
          },
          items: [
            {
              id: 3,
              produit_nom: 'Poulets de Chair Ross 308',
              quantite: 20,
              prix_unitaire: 15.00,
              total: 300.00
            }
          ],
          total_commande: 300.00,
          statut: 'confirmee',
          date_commande: '2025-01-07',
          date_livraison_prevue: '2025-01-10',
          mode_paiement: 'virement',
          notes: ''
        },
        {
          id: 3,
          numero_commande: 'CMD-2025-003',
          client: {
            nom: 'Mohamed Saidi',
            telephone: '0777345678',
            email: '<EMAIL>',
            adresse: 'Quartier Résidentiel, Constantine'
          },
          items: [
            {
              id: 4,
              produit_nom: 'Dindes Bronze',
              quantite: 5,
              prix_unitaire: 45.00,
              total: 225.00
            }
          ],
          total_commande: 225.00,
          statut: 'livree',
          date_commande: '2025-01-05',
          date_livraison_prevue: '2025-01-08',
          date_livraison_effective: '2025-01-08',
          mode_paiement: 'especes',
          notes: 'Client très satisfait'
        }
      ];

      // Filter orders based on status
      let filteredOrders = mockOrders;
      if (statusFilter !== 'all') {
        filteredOrders = mockOrders.filter(order => order.statut === statusFilter);
      }

      setOrders(filteredOrders);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des commandes');
      console.error('Erreur commandes:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Mock stats - replace with actual API call
      setStats({
        totalOrders: 25,
        pendingOrders: 8,
        completedOrders: 15,
        totalRevenue: 18750
      });
    } catch (err) {
      console.error('Erreur stats:', err);
    }
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedOrder(null);
  };

  const handleUpdateStatus = async (orderId, newStatus) => {
    try {
      console.log('Updating order status:', orderId, newStatus);
      // Update order status via API
      loadOrders();
      loadStats();
    } catch (err) {
      setError('Erreur lors de la mise à jour du statut');
      console.error('Erreur mise à jour:', err);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'en_attente': return 'warning';
      case 'confirmee': return 'info';
      case 'en_preparation': return 'primary';
      case 'expedie': return 'secondary';
      case 'livree': return 'success';
      case 'annulee': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'en_attente': return 'En Attente';
      case 'confirmee': return 'Confirmée';
      case 'en_preparation': return 'En Préparation';
      case 'expedie': return 'Expédiée';
      case 'livree': return 'Livrée';
      case 'annulee': return 'Annulée';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ShippingIcon color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Commandes
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalOrders}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PersonIcon color="warning" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    En Attente
                  </Typography>
                  <Typography variant="h5">
                    {stats.pendingOrders}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <CheckIcon color="success" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Terminées
                  </Typography>
                  <Typography variant="h5">
                    {stats.completedOrders}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <MoneyIcon color="info" sx={{ mr: 2 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Chiffre d'Affaires
                  </Typography>
                  <Typography variant="h5">
                    {stats.totalRevenue.toLocaleString()} DA
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          Gestion des Commandes
        </Typography>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Filtrer par statut</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <MenuItem value="all">Toutes</MenuItem>
            <MenuItem value="en_attente">En Attente</MenuItem>
            <MenuItem value="confirmee">Confirmées</MenuItem>
            <MenuItem value="en_preparation">En Préparation</MenuItem>
            <MenuItem value="expedie">Expédiées</MenuItem>
            <MenuItem value="livree">Livrées</MenuItem>
            <MenuItem value="annulee">Annulées</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Orders Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>N° Commande</TableCell>
              <TableCell>Client</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Total</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>
                  <Typography variant="subtitle2">
                    {order.numero_commande}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">
                      {order.client.nom}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {order.client.telephone}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>{order.date_commande}</TableCell>
                <TableCell>{order.total_commande.toLocaleString()} DA</TableCell>
                <TableCell>
                  <Chip
                    label={getStatusLabel(order.statut)}
                    color={getStatusColor(order.statut)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton size="small" onClick={() => handleViewOrder(order)}>
                    <ViewIcon />
                  </IconButton>
                  {order.statut === 'en_attente' && (
                    <IconButton 
                      size="small" 
                      onClick={() => handleUpdateStatus(order.id, 'confirmee')}
                    >
                      <CheckIcon />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Order Details Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        {selectedOrder && (
          <>
            <DialogTitle>
              Détails de la Commande {selectedOrder.numero_commande}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Informations Client
                  </Typography>
                  <Typography><strong>Nom:</strong> {selectedOrder.client.nom}</Typography>
                  <Typography><strong>Téléphone:</strong> {selectedOrder.client.telephone}</Typography>
                  <Typography><strong>Email:</strong> {selectedOrder.client.email}</Typography>
                  <Typography><strong>Adresse:</strong> {selectedOrder.client.adresse}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Informations Commande
                  </Typography>
                  <Typography><strong>Date:</strong> {selectedOrder.date_commande}</Typography>
                  <Typography><strong>Livraison prévue:</strong> {selectedOrder.date_livraison_prevue}</Typography>
                  <Typography><strong>Mode de paiement:</strong> {selectedOrder.mode_paiement}</Typography>
                  <Typography><strong>Statut:</strong> 
                    <Chip
                      label={getStatusLabel(selectedOrder.statut)}
                      color={getStatusColor(selectedOrder.statut)}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Articles Commandés
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Produit</TableCell>
                          <TableCell>Quantité</TableCell>
                          <TableCell>Prix Unitaire</TableCell>
                          <TableCell>Total</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedOrder.items.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell>{item.produit_nom}</TableCell>
                            <TableCell>{item.quantite}</TableCell>
                            <TableCell>{item.prix_unitaire} DA</TableCell>
                            <TableCell>{item.total} DA</TableCell>
                          </TableRow>
                        ))}
                        <TableRow>
                          <TableCell colSpan={3}><strong>Total Commande</strong></TableCell>
                          <TableCell><strong>{selectedOrder.total_commande} DA</strong></TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
                {selectedOrder.notes && (
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Notes
                    </Typography>
                    <Typography>{selectedOrder.notes}</Typography>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>
                Fermer
              </Button>
              {selectedOrder.statut === 'en_attente' && (
                <Button 
                  variant="contained" 
                  onClick={() => {
                    handleUpdateStatus(selectedOrder.id, 'confirmee');
                    handleCloseDialog();
                  }}
                >
                  Confirmer Commande
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default OrderManagement;
