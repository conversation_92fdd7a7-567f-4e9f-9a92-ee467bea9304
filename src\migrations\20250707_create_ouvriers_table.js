'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Ouvriers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      eleveur_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Eleveurs',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      nom: {
        type: Sequelize.STRING,
        allowNull: false
      },
      prenom: {
        type: Sequelize.STRING,
        allowNull: false
      },
      telephone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      adresse: {
        type: Sequelize.STRING,
        allowNull: true
      },
      date_embauche: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      salaire: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      statut: {
        type: Sequelize.ENUM('actif', 'inactif'),
        defaultValue: 'actif'
      },
      role: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'ouvrier'
      },
      horaires_travail: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Horaires de travail en format JSON'
      },
      competences: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
        defaultValue: []
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('Ouvriers', ['eleveur_id']);
    await queryInterface.addIndex('Ouvriers', ['telephone']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Ouvriers');
  }
};