const { <PERSON><PERSON>, User, <PERSON><PERSON><PERSON> } = require('../models');
const { Op } = require('sequelize');

// Obtenir toutes les ventes d'un éleveur
const getVentesByEleveur = async (req, res) => {
  try {
    const { eleveurId } = req.params;
    const { page = 1, limit = 10, statut, dateDebut, dateFin } = req.query;

    const offset = (page - 1) * limit;

    // Construire les conditions de recherche
    const whereConditions = {
      eleveur_id: eleveurId
    };

    if (statut) {
      whereConditions.statut = statut;
    }

    if (dateDebut && dateFin) {
      whereConditions.date_vente = {
        [Op.between]: [new Date(dateDebut), new Date(dateFin)]
      };
    }

    const ventes = await Vente.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'acheteur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: <PERSON><PERSON><PERSON>,
          as: 'volaille',
          attributes: ['id', 'espece', 'race']
        }
      ],
      order: [['date_vente', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        ventes: ventes.rows,
        pagination: {
          total: ventes.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(ventes.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des ventes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des ventes',
      error: error.message
    });
  }
};

// Créer une nouvelle vente
const createVente = async (req, res) => {
  try {
    const {
      eleveur_id,
      acheteur_id,
      volaille_id,
      quantite,
      prix_unitaire,
      notes
    } = req.body;

    // Validation des données requises
    if (!eleveur_id || !quantite || !prix_unitaire) {
      return res.status(400).json({
        success: false,
        message: 'Les champs eleveur_id, quantite et prix_unitaire sont requis'
      });
    }

    // Calculer le montant total
    const montant_total = quantite * prix_unitaire;

    const nouvelleVente = await Vente.create({
      eleveur_id,
      acheteur_id,
      volaille_id,
      quantite,
      prix_unitaire,
      montant_total,
      notes,
      statut: 'en_cours'
    });

    // Récupérer la vente créée avec les associations
    const venteComplete = await Vente.findByPk(nouvelleVente.id, {
      include: [
        {
          model: User,
          as: 'acheteur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: Volaille,
          as: 'produitVendu',
          attributes: ['id', 'type', 'race']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Vente créée avec succès',
      data: venteComplete
    });
  } catch (error) {
    console.error('Erreur lors de la création de la vente:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la vente',
      error: error.message
    });
  }
};

// Mettre à jour une vente
const updateVente = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Recalculer le montant total si quantité ou prix unitaire changent
    if (updateData.quantite || updateData.prix_unitaire) {
      const vente = await Vente.findByPk(id);
      if (!vente) {
        return res.status(404).json({
          success: false,
          message: 'Vente non trouvée'
        });
      }

      const quantite = updateData.quantite || vente.quantite;
      const prix_unitaire = updateData.prix_unitaire || vente.prix_unitaire;
      updateData.montant_total = quantite * prix_unitaire;
    }

    const [updatedRowsCount] = await Vente.update(updateData, {
      where: { id }
    });

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Vente non trouvée'
      });
    }

    // Récupérer la vente mise à jour
    const venteMiseAJour = await Vente.findByPk(id, {
      include: [
        {
          model: User,
          as: 'acheteur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: Volaille,
          as: 'produitVendu',
          attributes: ['id', 'type', 'race']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Vente mise à jour avec succès',
      data: venteMiseAJour
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la vente:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la vente',
      error: error.message
    });
  }
};

// Supprimer une vente
const deleteVente = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedRowsCount = await Vente.destroy({
      where: { id }
    });

    if (deletedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Vente non trouvée'
      });
    }

    res.json({
      success: true,
      message: 'Vente supprimée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de la vente:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la vente',
      error: error.message
    });
  }
};

// Obtenir les statistiques de vente pour un éleveur
const getStatistiquesVentes = async (req, res) => {
  try {
    const { eleveurId } = req.params;
    const { periode = 'mois' } = req.query; // mois, trimestre, annee

    let dateDebut;
    const maintenant = new Date();

    switch (periode) {
      case 'mois':
        dateDebut = new Date(maintenant.getFullYear(), maintenant.getMonth(), 1);
        break;
      case 'trimestre':
        const trimestre = Math.floor(maintenant.getMonth() / 3);
        dateDebut = new Date(maintenant.getFullYear(), trimestre * 3, 1);
        break;
      case 'annee':
        dateDebut = new Date(maintenant.getFullYear(), 0, 1);
        break;
      default:
        dateDebut = new Date(maintenant.getFullYear(), maintenant.getMonth(), 1);
    }

    const [totalVentes, ventesConfirmees, chiffreAffaires, ventesParStatut] = await Promise.all([
      // Total des ventes
      Vente.count({
        where: {
          eleveur_id: eleveurId,
          date_vente: {
            [Op.gte]: dateDebut
          }
        }
      }),

      // Ventes confirmées
      Vente.count({
        where: {
          eleveur_id: eleveurId,
          statut: 'completee',
          date_vente: {
            [Op.gte]: dateDebut
          }
        }
      }),

      // Chiffre d'affaires
      Vente.sum('montant_total', {
        where: {
          eleveur_id: eleveurId,
          statut: 'completee',
          date_vente: {
            [Op.gte]: dateDebut
          }
        }
      }),

      // Répartition par statut
      Vente.findAll({
        where: {
          eleveur_id: eleveurId,
          date_vente: {
            [Op.gte]: dateDebut
          }
        },
        attributes: [
          'statut',
          [Vente.sequelize.fn('COUNT', Vente.sequelize.col('id')), 'count'],
          [Vente.sequelize.fn('SUM', Vente.sequelize.col('montant_total')), 'total']
        ],
        group: ['statut'],
        raw: true
      })
    ]);

    res.json({
      success: true,
      data: {
        periode,
        total_ventes: totalVentes,
        ventes_confirmees: ventesConfirmees,
        chiffre_affaires: chiffreAffaires || 0,
        repartition_statut: ventesParStatut
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
};

// Obtenir une vente par ID
const getVenteById = async (req, res) => {
  try {
    const { id } = req.params;

    const vente = await Vente.findByPk(id, {
      include: [
        {
          model: User,
          as: 'acheteur',
          attributes: ['id', 'nom', 'email', 'telephone']
        },
        {
          model: Volaille,
          as: 'produitVendu',
          attributes: ['id', 'type', 'race']
        }
      ]
    });

    if (!vente) {
      return res.status(404).json({
        success: false,
        message: 'Vente non trouvée'
      });
    }

    res.json({
      success: true,
      data: vente
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de la vente:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la vente',
      error: error.message
    });
  }
};

module.exports = {
  getVentesByEleveur,
  createVente,
  updateVente,
  deleteVente,
  getStatistiquesVentes,
  getVenteById
};
