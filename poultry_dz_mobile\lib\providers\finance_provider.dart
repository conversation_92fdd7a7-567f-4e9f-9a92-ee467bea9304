import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction_model.dart';
import '../services/finance_service.dart';

class FinanceState {
  final List<Transaction> transactions;
  final bool isLoading;
  final String? error;
  final double balance;
  final Map<String, double> budgetAllocations;
  final Map<String, double> expenses;

  FinanceState({
    this.transactions = const [],
    this.isLoading = false,
    this.error,
    this.balance = 0.0,
    this.budgetAllocations = const {},
    this.expenses = const {},
  });

  FinanceState copyWith({
    List<Transaction>? transactions,
    bool? isLoading,
    String? error,
    double? balance,
    Map<String, double>? budgetAllocations,
    Map<String, double>? expenses,
  }) {
    return FinanceState(
      transactions: transactions ?? this.transactions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      balance: balance ?? this.balance,
      budgetAllocations: budgetAllocations ?? this.budgetAllocations,
      expenses: expenses ?? this.expenses,
    );
  }
}

class FinanceNotifier extends StateNotifier<FinanceState> {
  final FinanceService _financeService;

  FinanceNotifier(this._financeService) : super(FinanceState());

  Future<void> loadTransactions(int farmId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final transactions = await _financeService.getTransactions(farmId);
      final balance = await _financeService.getBalance(farmId);
      state = state.copyWith(
        transactions: transactions,
        balance: balance,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> addTransaction(Transaction transaction) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _financeService.addTransaction(transaction);
      await loadTransactions(transaction.farmId);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> updateTransaction(Transaction transaction) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _financeService.updateTransaction(transaction);
      await loadTransactions(transaction.farmId);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> deleteTransaction(int transactionId, int farmId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _financeService.deleteTransaction(transactionId);
      await loadTransactions(farmId);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadBudgetAllocations(int farmId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final allocations = await _financeService.getBudgetAllocations(farmId);
      state = state.copyWith(
        budgetAllocations: allocations,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> updateBudgetAllocation(String category, double amount, int farmId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _financeService.updateBudgetAllocation(category, amount, farmId);
      await loadBudgetAllocations(farmId);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

final financeProvider = StateNotifierProvider<FinanceNotifier, FinanceState>((ref) {
  final financeService = ref.watch(financeServiceProvider);
  return FinanceNotifier(financeService);
});