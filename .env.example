# Poultray DZ Platform - Environment Configuration
# Copy this file to .env and update the values as needed

# ===========================================
# SERVER CONFIGURATION
# ===========================================
PORT=3003
NODE_ENV=development

# ===========================================
# DATABASE CONFIGURATION (PostgreSQL)
# ===========================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=poultraydz
DB_USER=postgres
DB_PASSWORD=root

# ===========================================
# WEATHER API INTEGRATION (Optional)
# ===========================================
# Get your API key from: https://openweathermap.org/api
OPENWEATHERMAP_API_KEY=your_openweathermap_api_key_here

# Alternative weather API: https://www.weatherapi.com/
WEATHER_API_KEY=your_weatherapi_key_here

# ===========================================
# IOT/MQTT CONFIGURATION (Optional)
# ===========================================
# MQTT Broker URL (use local broker or cloud service like HiveMQ)
MQTT_BROKER_URL=mqtt://localhost:1883
MQTT_USERNAME=your_mqtt_username
MQTT_PASSWORD=your_mqtt_password

# WebSocket server port for real-time IoT data
WS_PORT=8080

# ===========================================
# AI/ML SERVICES (Optional)
# ===========================================
# OpenAI API for AI recommendations
OPENAI_API_KEY=your_openai_api_key_here

# ===========================================
# EMAIL CONFIGURATION (Optional)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# ===========================================
# FIREBASE CONFIGURATION (Optional)
# ===========================================
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# ===========================================
# DEVELOPMENT/TESTING
# ===========================================
# Set to true to enable detailed SQL query logging
DEBUG_SQL=false

# Set to true to enable mock data generation
ENABLE_MOCK_DATA=true

# ===========================================
# PRODUCTION SETTINGS (for deployment)
# ===========================================
# Uncomment and configure for production deployment
# CORS_ORIGIN=https://yourdomain.com
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100
# ENABLE_HTTPS=true
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
