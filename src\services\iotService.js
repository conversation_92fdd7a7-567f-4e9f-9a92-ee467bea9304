const EventEmitter = require('events');

// Optional dependencies - gracefully handle if not installed
let mqtt = null;
let WebSocket = null;

try {
  mqtt = require('mqtt');
} catch (error) {
  console.log('⚠️ MQTT package not installed - IoT service will run in simulation mode');
}

try {
  WebSocket = require('ws');
} catch (error) {
  console.log('⚠️ WebSocket package not installed - real-time features disabled');
}

/**
 * Service de gestion des dispositifs IoT
 * Gère la communication MQTT, WebSocket et les données des capteurs
 */
class IoTService extends EventEmitter {
  constructor() {
    super();

    // Configuration MQTT
    this.mqttClient = null;
    this.mqttBrokerUrl = process.env.MQTT_BROKER_URL || 'mqtt://localhost:1883';
    this.mqttOptions = {
      clientId: 'poultraydz-server-' + Math.random().toString(16).substr(2, 8),
      username: process.env.MQTT_USERNAME,
      password: process.env.MQTT_PASSWORD,
      keepalive: 60,
      reconnectPeriod: 30000, // Reduced reconnection attempts (30 seconds instead of 1 second)
      clean: true
    };

    // Configuration WebSocket
    this.wsServer = null;
    this.wsClients = new Set();

    // Stockage des données des dispositifs
    this.devices = new Map();
    this.sensorData = new Map();
    this.alerts = new Map();

    // Topics MQTT
    this.topics = {
      deviceStatus: 'poultraydz/devices/+/status',
      sensorData: 'poultraydz/sensors/+/data',
      commands: 'poultraydz/commands/+',
      alerts: 'poultraydz/alerts/+'
    };

    this.initializeMQTT();
    this.initializeWebSocket();
    this.initializeMockDevices();
  }

  /**
   * Initialiser la connexion MQTT
   */
  initializeMQTT() {
    if (!mqtt) {
      console.log('📡 MQTT non disponible - mode simulation activé');
      return;
    }

    try {
      this.mqttClient = mqtt.connect(this.mqttBrokerUrl, this.mqttOptions);

      this.mqttClient.on('connect', () => {
        console.log('✅ Connexion MQTT établie');
        this.subscribeToTopics();
      });

      this.mqttClient.on('message', (topic, message) => {
        this.handleMQTTMessage(topic, message);
      });

      this.mqttClient.on('error', (error) => {
        console.error('❌ Erreur MQTT:', error);
      });

      this.mqttClient.on('offline', () => {
        console.log('⚠️ MQTT hors ligne');
      });

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation MQTT:', error);
      // Continuer sans MQTT en mode simulation
    }
  }

  /**
   * S'abonner aux topics MQTT
   */
  subscribeToTopics() {
    Object.values(this.topics).forEach(topic => {
      this.mqttClient.subscribe(topic, (err) => {
        if (err) {
          console.error(`❌ Erreur abonnement topic ${topic}:`, err);
        } else {
          console.log(`✅ Abonné au topic: ${topic}`);
        }
      });
    });
  }

  /**
   * Traiter les messages MQTT reçus
   */
  handleMQTTMessage(topic, message) {
    try {
      const data = JSON.parse(message.toString());
      const topicParts = topic.split('/');

      if (topic.includes('/devices/') && topic.includes('/status')) {
        // Statut des dispositifs
        const deviceId = topicParts[2];
        this.updateDeviceStatus(deviceId, data);
      } else if (topic.includes('/sensors/') && topic.includes('/data')) {
        // Données des capteurs
        const sensorId = topicParts[2];
        this.updateSensorData(sensorId, data);
      } else if (topic.includes('/alerts/')) {
        // Alertes
        const deviceId = topicParts[2];
        this.handleAlert(deviceId, data);
      }

      // Diffuser les données via WebSocket
      this.broadcastToWebSocketClients({
        type: 'mqtt_message',
        topic,
        data
      });

    } catch (error) {
      console.error('❌ Erreur traitement message MQTT:', error);
    }
  }

  /**
   * Initialiser le serveur WebSocket
   */
  initializeWebSocket() {
    if (!WebSocket) {
      console.log('🔌 WebSocket non disponible - fonctionnalités temps réel désactivées');
      return;
    }

    try {
      const port = process.env.WS_PORT || 8081; // Changed from 8080 to 8081
      this.wsServer = new WebSocket.Server({ port });

      this.wsServer.on('connection', (ws) => {
        console.log('✅ Nouvelle connexion WebSocket');
        this.wsClients.add(ws);

        // Envoyer les données actuelles au nouveau client
        ws.send(JSON.stringify({
          type: 'initial_data',
          devices: Array.from(this.devices.values()),
          sensorData: Array.from(this.sensorData.entries()),
          alerts: Array.from(this.alerts.values())
        }));

        ws.on('message', (message) => {
          this.handleWebSocketMessage(ws, message);
        });

        ws.on('close', () => {
          console.log('❌ Connexion WebSocket fermée');
          this.wsClients.delete(ws);
        });

        ws.on('error', (error) => {
          console.error('❌ Erreur WebSocket:', error);
          this.wsClients.delete(ws);
        });
      });

      console.log(`✅ Serveur WebSocket démarré sur le port ${port}`);
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation WebSocket:', error);
    }
  }

  /**
   * Traiter les messages WebSocket
   */
  handleWebSocketMessage(ws, message) {
    try {
      const data = JSON.parse(message.toString());

      switch (data.type) {
        case 'command':
          this.sendCommand(data.deviceId, data.command, data.parameters);
          break;
        case 'get_devices':
          ws.send(JSON.stringify({
            type: 'devices_list',
            devices: Array.from(this.devices.values())
          }));
          break;
        case 'get_sensor_data':
          ws.send(JSON.stringify({
            type: 'sensor_data',
            data: Array.from(this.sensorData.entries())
          }));
          break;
      }
    } catch (error) {
      console.error('❌ Erreur traitement message WebSocket:', error);
    }
  }

  /**
   * Diffuser un message à tous les clients WebSocket
   */
  broadcastToWebSocketClients(message) {
    if (!WebSocket || this.wsClients.size === 0) {
      return;
    }

    const messageStr = JSON.stringify(message);
    this.wsClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageStr);
      }
    });
  }

  /**
   * Mettre à jour le statut d'un dispositif
   */
  updateDeviceStatus(deviceId, statusData) {
    const device = this.devices.get(deviceId) || {
      id: deviceId,
      name: `Dispositif ${deviceId}`,
      type: 'unknown',
      location: 'Non défini'
    };

    device.status = statusData.status || 'unknown';
    device.battery_level = statusData.battery_level;
    device.signal_strength = statusData.signal_strength;
    device.last_update = new Date().toISOString();

    this.devices.set(deviceId, device);
    this.emit('deviceStatusUpdated', device);
  }

  /**
   * Mettre à jour les données d'un capteur
   */
  updateSensorData(sensorId, data) {
    const timestamp = new Date().toISOString();

    if (!this.sensorData.has(sensorId)) {
      this.sensorData.set(sensorId, []);
    }

    const sensorHistory = this.sensorData.get(sensorId);
    sensorHistory.push({
      ...data,
      timestamp
    });

    // Garder seulement les 100 dernières mesures
    if (sensorHistory.length > 100) {
      sensorHistory.shift();
    }

    this.sensorData.set(sensorId, sensorHistory);
    this.emit('sensorDataUpdated', { sensorId, data: { ...data, timestamp } });

    // Vérifier les seuils et générer des alertes si nécessaire
    this.checkThresholds(sensorId, data);
  }

  /**
   * Vérifier les seuils et générer des alertes
   */
  checkThresholds(sensorId, data) {
    const alerts = [];

    // Seuils de température
    if (data.temperature !== undefined) {
      if (data.temperature > 35) {
        alerts.push({
          type: 'warning',
          severity: 'high',
          message: `Température élevée détectée: ${data.temperature}°C`,
          sensorId,
          value: data.temperature,
          threshold: 35
        });
      } else if (data.temperature < 5) {
        alerts.push({
          type: 'warning',
          severity: 'high',
          message: `Température basse détectée: ${data.temperature}°C`,
          sensorId,
          value: data.temperature,
          threshold: 5
        });
      }
    }

    // Seuils d'humidité
    if (data.humidity !== undefined) {
      if (data.humidity > 85) {
        alerts.push({
          type: 'warning',
          severity: 'medium',
          message: `Humidité élevée détectée: ${data.humidity}%`,
          sensorId,
          value: data.humidity,
          threshold: 85
        });
      } else if (data.humidity < 30) {
        alerts.push({
          type: 'warning',
          severity: 'medium',
          message: `Humidité basse détectée: ${data.humidity}%`,
          sensorId,
          value: data.humidity,
          threshold: 30
        });
      }
    }

    // Traiter les alertes
    alerts.forEach(alert => {
      this.handleAlert(sensorId, alert);
    });
  }

  /**
   * Traiter une alerte
   */
  handleAlert(deviceId, alertData) {
    const alertId = `${deviceId}_${Date.now()}`;
    const alert = {
      id: alertId,
      deviceId,
      ...alertData,
      timestamp: new Date().toISOString(),
      acknowledged: false
    };

    this.alerts.set(alertId, alert);
    this.emit('alertGenerated', alert);

    // Diffuser l'alerte via WebSocket
    this.broadcastToWebSocketClients({
      type: 'alert',
      alert
    });
  }

  /**
   * Envoyer une commande à un dispositif
   */
  sendCommand(deviceId, command, parameters = {}) {
    const commandData = {
      command,
      parameters,
      timestamp: new Date().toISOString(),
      requestId: Math.random().toString(36).substr(2, 9)
    };

    if (mqtt && this.mqttClient && this.mqttClient.connected) {
      const topic = `poultraydz/commands/${deviceId}`;
      this.mqttClient.publish(topic, JSON.stringify(commandData));
      console.log(`📤 Commande envoyée à ${deviceId}:`, command);
    } else {
      console.log(`📤 Simulation commande pour ${deviceId}:`, command);
      // Simuler une réponse
      setTimeout(() => {
        this.handleCommandResponse(deviceId, commandData, { status: 'success' });
      }, 1000);
    }

    return commandData.requestId;
  }

  /**
   * Traiter la réponse d'une commande
   */
  handleCommandResponse(deviceId, command, response) {
    this.emit('commandResponse', { deviceId, command, response });

    this.broadcastToWebSocketClients({
      type: 'command_response',
      deviceId,
      command,
      response
    });
  }

  /**
   * Obtenir tous les dispositifs
   */
  getDevices() {
    return Array.from(this.devices.values());
  }

  /**
   * Obtenir les données d'un capteur
   */
  getSensorData(sensorId, limit = 50) {
    const data = this.sensorData.get(sensorId) || [];
    return data.slice(-limit);
  }

  /**
   * Obtenir toutes les alertes
   */
  getAlerts(acknowledged = null) {
    let alerts = Array.from(this.alerts.values());

    if (acknowledged !== null) {
      alerts = alerts.filter(alert => alert.acknowledged === acknowledged);
    }

    return alerts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  /**
   * Marquer une alerte comme acquittée
   */
  acknowledgeAlert(alertId) {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = new Date().toISOString();
      this.alerts.set(alertId, alert);

      this.broadcastToWebSocketClients({
        type: 'alert_acknowledged',
        alertId,
        alert
      });
    }
  }

  /**
   * Initialiser des dispositifs simulés pour les tests
   */
  initializeMockDevices() {
    const mockDevices = [
      {
        id: 'temp_sensor_01',
        name: 'Capteur Température Poulailler 1',
        type: 'temperature',
        location: 'Poulailler Principal',
        status: 'active',
        battery_level: 85,
        signal_strength: -45
      },
      {
        id: 'humidity_sensor_01',
        name: 'Capteur Humidité Poulailler 1',
        type: 'humidity',
        location: 'Poulailler Principal',
        status: 'active',
        battery_level: 78,
        signal_strength: -52
      },
      {
        id: 'feeder_01',
        name: 'Distributeur Aliment Automatique',
        type: 'feeder',
        location: 'Zone Alimentation',
        status: 'active',
        battery_level: 92,
        signal_strength: -38
      }
    ];

    mockDevices.forEach(device => {
      device.last_update = new Date().toISOString();
      this.devices.set(device.id, device);
    });

    // Simuler des données de capteurs
    this.startMockDataGeneration();
  }

  /**
   * Démarrer la génération de données simulées
   */
  startMockDataGeneration() {
    setInterval(() => {
      // Générer des données de température
      this.updateSensorData('temp_sensor_01', {
        temperature: 20 + Math.random() * 15,
        unit: '°C'
      });

      // Générer des données d'humidité
      this.updateSensorData('humidity_sensor_01', {
        humidity: 50 + Math.random() * 30,
        unit: '%'
      });

      // Générer des données du distributeur
      this.updateSensorData('feeder_01', {
        feed_level: 30 + Math.random() * 70,
        unit: '%'
      });
    }, 30000); // Toutes les 30 secondes
  }
}

module.exports = new IoTService();
