const jwt = require('jsonwebtoken');
const { auth } = require('../middleware/auth');
const { checkRole, isEleveur, isOuvrier } = require('../middleware/checkRole');
const { User, Role, Eleveur, Ouvrier } = require('../models');

describe('Middleware Tests', () => {
  let adminRole, eleveurRole, ouvrierRole;
  let adminUser, eleveurUser, ouvrierUser;
  let eleveur, ouvrier;

  beforeAll(async () => {
    // Créer les rôles
    adminRole = await Role.create({
      name: 'admin',
      description: 'Admin role',
      permissions: ['manage_users', 'manage_ouvriers']
    });

    eleveurRole = await Role.create({
      name: 'eleveur',
      description: 'Eleveur role',
      permissions: ['manage_own_ouvriers']
    });

    ouvrierRole = await Role.create({
      name: 'ouvrier',
      description: 'Ouvrier role',
      permissions: ['view_own_profile']
    });

    // C<PERSON>er un éleveur
    eleveur = await Eleveur.create({
      nom: 'Test',
      prenom: 'Eleveur',
      email: '<EMAIL>',
      telephone: '0123456789',
      adresse: 'Test address'
    });

    // Créer un ouvrier
    ouvrier = await Ouvrier.create({
      nom: 'Test',
      prenom: 'Ouvrier',
      email: '<EMAIL>',
      telephone: '0123456789',
      adresse: 'Test address',
      date_embauche: new Date(),
      salaire: 30000,
      eleveur_id: eleveur.id
    });

    // Créer les utilisateurs
    adminUser = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password: 'Admin123!',
      role_id: adminRole.id,
      status: 'active'
    });

    eleveurUser = await User.create({
      username: 'testeleveur',
      email: '<EMAIL>',
      password: 'Eleveur123!',
      role_id: eleveurRole.id,
      profile_id: eleveur.id,
      status: 'active'
    });

    ouvrierUser = await User.create({
      username: 'testouvrier',
      email: '<EMAIL>',
      password: 'Ouvrier123!',
      role_id: ouvrierRole.id,
      profile_id: ouvrier.id,
      status: 'active'
    });
  });

  afterAll(async () => {
    // Nettoyer la base de données
    await User.destroy({ where: {} });
    await Role.destroy({ where: {} });
    await Eleveur.destroy({ where: {} });
    await Ouvrier.destroy({ where: {} });
  });

  describe('auth middleware', () => {
    it('should pass with valid token', async () => {
      const token = await adminUser.generateToken();
      const { req, res, next } = global.mockExpressHandler();
      req.headers['authorization'] = `Bearer ${token}`;

      await auth(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(req.user).toBeDefined();
      expect(req.user.id).toBe(adminUser.id);
    });

    it('should fail with invalid token', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.headers['authorization'] = 'Bearer invalid.token.here';

      await auth(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.any(String)
      }));
    });

    it('should fail with no token', async () => {
      const { req, res, next } = global.mockExpressHandler();

      await auth(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Token non fourni'
      }));
    });
  });

  describe('checkRole middleware', () => {
    it('should pass with correct role', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = { role: 'admin' };

      checkRole(['admin'])(req, res, next);

      expect(next).toHaveBeenCalled();
    });

    it('should fail with incorrect role', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = { role: 'ouvrier' };

      checkRole(['admin'])(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.any(String)
      }));
    });
  });

  describe('isEleveur middleware', () => {
    it('should pass for eleveur accessing own resource', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = {
        role: 'eleveur',
        profile_id: eleveur.id
      };
      req.params = { eleveurId: eleveur.id.toString() };

      await isEleveur(req, res, next);

      expect(next).toHaveBeenCalled();
    });

    it('should fail for eleveur accessing other resource', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = {
        role: 'eleveur',
        profile_id: eleveur.id
      };
      req.params = { eleveurId: '999' };

      await isEleveur(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
    });

    it('should pass for admin', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = { role: 'admin' };
      req.params = { eleveurId: '999' };

      await isEleveur(req, res, next);

      expect(next).toHaveBeenCalled();
    });
  });

  describe('isOuvrier middleware', () => {
    it('should pass for ouvrier accessing own resource', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = {
        role: 'ouvrier',
        profile_id: ouvrier.id
      };
      req.params = { ouvrierId: ouvrier.id.toString() };

      await isOuvrier(req, res, next);

      expect(next).toHaveBeenCalled();
    });

    it('should fail for ouvrier accessing other resource', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = {
        role: 'ouvrier',
        profile_id: ouvrier.id
      };
      req.params = { ouvrierId: '999' };

      await isOuvrier(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
    });

    it('should pass for admin', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = { role: 'admin' };
      req.params = { ouvrierId: '999' };

      await isOuvrier(req, res, next);

      expect(next).toHaveBeenCalled();
    });

    it('should pass for eleveur accessing own ouvrier', async () => {
      const { req, res, next } = global.mockExpressHandler();
      req.user = {
        role: 'eleveur',
        profile_id: eleveur.id
      };
      req.params = { ouvrierId: ouvrier.id.toString() };

      await isOuvrier(req, res, next);

      expect(next).toHaveBeenCalled();
    });
  });
});