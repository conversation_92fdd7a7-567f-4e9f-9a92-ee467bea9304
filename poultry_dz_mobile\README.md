# Poultry DZ Mobile

Application mobile pour la gestion d'élevage de volailles.

## Configuration requise

- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code avec les extensions Flutter et Dart
- JDK (>=11)
- Android SDK (pour Android)
- Xcode (pour iOS)

## Installation

1. <PERSON><PERSON><PERSON> le dépôt :
```bash
git clone [url_du_depot]
cd poultry_dz_mobile
```

2. Installer les dépendances :
```bash
flutter pub get
```

3. Générer les assets et les fichiers nécessaires :
```bash
flutter pub run flutter_launcher_icons
flutter pub run flutter_native_splash:create
```

4. Lancer l'application :
```bash
flutter run
```

## Structure du projet

```
lib/
├── main.dart                 # Point d'entrée de l'application
├── models/                   # Modèles de données
│   ├── user_model.dart
│   └── auth_models.dart
├── providers/               # Gestion d'état avec Riverpod
│   └── auth_provider.dart
├── screens/                 # Écrans de l'application
│   ├── auth/
│   ├── dashboard/
│   └── splash/
├── services/               # Services pour la logique métier
│   ├── api_service.dart
│   ├── auth_service.dart
│   ├── biometric_service.dart
│   └── storage_service.dart
├── theme/                  # Configuration du thème
│   └── app_theme.dart
├── utils/                  # Utilitaires
│   └── app_config.dart
└── navigation/             # Gestion de la navigation
    └── app_navigator.dart
```

## Fonctionnalités

- Authentification (Login, Register, Reset Password)
- Authentification biométrique
- Gestion des profils utilisateurs
- Tableau de bord personnalisé par rôle
- Gestion des élevages
- Suivi de la santé des volailles
- Gestion de l'alimentation
- Rapports et statistiques
- Notifications
- Support multilingue (Français, Arabe)
- Mode hors ligne

## Architecture

L'application utilise :
- **Riverpod** pour la gestion d'état
- **HTTP** pour les appels API
- **SharedPreferences** pour le stockage local
- **LocalAuth** pour l'authentification biométrique

## Tests

Exécuter les tests :
```bash
flutter test
```

Générer un rapport de couverture :
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## Déploiement

### Android

1. Configurer le fichier `key.properties`
2. Construire l'APK :
```bash
flutter build apk --release
```

### iOS

1. Configurer les certificats dans Xcode
2. Construire l'IPA :
```bash
flutter build ips --release
```

## Contribution

1. Fork le projet
2. Créer une branche (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -m 'Ajout d'une nouvelle fonctionnalité'`)
4. Push la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.